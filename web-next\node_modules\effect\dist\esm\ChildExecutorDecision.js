/**
 * @since 2.0.0
 */
import * as internal from "./internal/channel/childExecutorDecision.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const ChildExecutorDecisionTypeId = internal.ChildExecutorDecisionTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Continue = internal.Continue;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Close = internal.Close;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Yield = internal.Yield;
/**
 * Returns `true` if the specified value is a `ChildExecutorDecision`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isChildExecutorDecision = internal.isChildExecutorDecision;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Continue`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isContinue = internal.isContinue;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Close`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isClose = internal.isClose;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Yield`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isYield = internal.isYield;
/**
 * Folds over a `ChildExecutorDecision` to produce a value of type `A`.
 *
 * @since 2.0.0
 * @category folding
 */
export const match = internal.match;
//# sourceMappingURL=ChildExecutorDecision.js.map