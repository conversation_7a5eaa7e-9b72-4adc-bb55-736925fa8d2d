"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.int8Array = int8Array;
const globals_1 = require("../utils/globals");
const integer_1 = require("./integer");
const TypedIntArrayArbitraryBuilder_1 = require("./_internals/builders/TypedIntArrayArbitraryBuilder");
function int8Array(constraints = {}) {
    return (0, TypedIntArrayArbitraryBuilder_1.typedIntArrayArbitraryArbitraryBuilder)(constraints, -128, 127, globals_1.Int8Array, integer_1.integer);
}
