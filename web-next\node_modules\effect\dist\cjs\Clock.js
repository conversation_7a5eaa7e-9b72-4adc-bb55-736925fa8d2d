"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sleep = exports.make = exports.currentTimeNanos = exports.currentTimeMillis = exports.clockWith = exports.ClockTypeId = exports.Clock = void 0;
var internal = _interopRequireWildcard(require("./internal/clock.js"));
var defaultServices = _interopRequireWildcard(require("./internal/defaultServices.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const ClockTypeId = exports.ClockTypeId = internal.ClockTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
const sleep = exports.sleep = defaultServices.sleep;
/**
 * @since 2.0.0
 * @category constructors
 */
const currentTimeMillis = exports.currentTimeMillis = defaultServices.currentTimeMillis;
/**
 * @since 2.0.0
 * @category constructors
 */
const currentTimeNanos = exports.currentTimeNanos = defaultServices.currentTimeNanos;
/**
 * @since 2.0.0
 * @category constructors
 */
const clockWith = exports.clockWith = defaultServices.clockWith;
/**
 * @since 2.0.0
 * @category context
 */
const Clock = exports.Clock = internal.clockTag;
//# sourceMappingURL=Clock.js.map