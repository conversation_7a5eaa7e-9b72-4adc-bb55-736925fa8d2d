"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.unnested = exports.nested = exports.mapName = exports.empty = exports.andThen = void 0;
var internal = _interopRequireWildcard(require("./internal/configProvider/pathPatch.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category constructors
 */
const empty = exports.empty = internal.empty;
/**
 * @since 2.0.0
 * @category constructors
 */
const andThen = exports.andThen = internal.andThen;
/**
 * @since 2.0.0
 * @category constructors
 */
const mapName = exports.mapName = internal.mapName;
/**
 * @since 2.0.0
 * @category constructors
 */
const nested = exports.nested = internal.nested;
/**
 * @since 2.0.0
 * @category constructors
 */
const unnested = exports.unnested = internal.unnested;
//# sourceMappingURL=ConfigProviderPathPatch.js.map