import type { PathLike } from "node:fs";
/**
* Does the current process have {@link mode} access?
* By default, checks if the path is visible to the proccess.
*
* @param mode A `fs.constants` value; default `F_OK`
*/
export declare function ok(path: PathLike, mode?: number): boolean;
/**
* Can the current process write to this path?
*/
export declare function writable(path: PathLike): boolean;
/**
* Can the current process read this path?
*/
export declare function readable(path: PathLike): boolean;
/**
* Can the current process execute this path?
*/
export declare function executable(path: PathLike): boolean;
