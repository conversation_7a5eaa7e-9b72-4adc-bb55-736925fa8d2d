var Os=Object.defineProperty;var fi=i=>{throw TypeError(i)};var n=(i,o)=>Os(i,"name",{value:o,configurable:!0});var ci=(i,o,a)=>o.has(i)||fi("Cannot "+a);var O=(i,o,a)=>(ci(i,o,"read from private field"),a?a.call(i):o.get(i)),be=(i,o,a)=>o.has(i)?fi("Cannot add the same private member more than once"):o instanceof WeakSet?o.add(i):o.set(i,a),X=(i,o,a,f)=>(ci(i,o,"write to private field"),f?f.call(i,a):o.set(i,a),a);var ve,zt,bt,Cr,ze,It,Ft,mt,ee,yt,He,Ve,gt;import Bt from"node:http";import zs from"node:https";import st from"node:zlib";import me,{PassThrough as dr,pipeline as lt}from"node:stream";import{Buffer as M}from"node:buffer";import{types as hr,deprecate as pr,promisify as Is}from"node:util";import{c as di,g as Fs}from"./shared/node-fetch-native.DfbY2q-x.mjs";import{format as js}from"node:url";import{isIP as Ls}from"node:net";import{promises as $s,statSync as hi,createReadStream as Ds}from"node:fs";import{basename as Ms}from"node:path";function Us(i){if(!/^data:/i.test(i))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');i=i.replace(/\r?\n/g,"");const o=i.indexOf(",");if(o===-1||o<=4)throw new TypeError("malformed data: URI");const a=i.substring(5,o).split(";");let f="",l=!1;const p=a[0]||"text/plain";let h=p;for(let A=1;A<a.length;A++)a[A]==="base64"?l=!0:a[A]&&(h+=`;${a[A]}`,a[A].indexOf("charset=")===0&&(f=a[A].substring(8)));!a[0]&&!f.length&&(h+=";charset=US-ASCII",f="US-ASCII");const S=l?"base64":"ascii",v=unescape(i.substring(o+1)),w=Buffer.from(v,S);return w.type=p,w.typeFull=h,w.charset=f,w}n(Us,"dataUriToBuffer");var pi={},kt={exports:{}};/**
 * @license
 * web-streams-polyfill v3.3.3
 * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */var xs=kt.exports,bi;function Ns(){return bi||(bi=1,function(i,o){(function(a,f){f(o)})(xs,function(a){function f(){}n(f,"noop");function l(e){return typeof e=="object"&&e!==null||typeof e=="function"}n(l,"typeIsObject");const p=f;function h(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch{}}n(h,"setFunctionName");const S=Promise,v=Promise.prototype.then,w=Promise.reject.bind(S);function A(e){return new S(e)}n(A,"newPromise");function T(e){return A(t=>t(e))}n(T,"promiseResolvedWith");function b(e){return w(e)}n(b,"promiseRejectedWith");function q(e,t,r){return v.call(e,t,r)}n(q,"PerformPromiseThen");function g(e,t,r){q(q(e,t,r),void 0,p)}n(g,"uponPromise");function V(e,t){g(e,t)}n(V,"uponFulfillment");function I(e,t){g(e,void 0,t)}n(I,"uponRejection");function F(e,t,r){return q(e,t,r)}n(F,"transformPromiseWith");function Q(e){q(e,void 0,p)}n(Q,"setPromiseIsHandledToTrue");let ge=n(e=>{if(typeof queueMicrotask=="function")ge=queueMicrotask;else{const t=T(void 0);ge=n(r=>q(t,r),"_queueMicrotask")}return ge(e)},"_queueMicrotask");function z(e,t,r){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}n(z,"reflectCall");function j(e,t,r){try{return T(z(e,t,r))}catch(s){return b(s)}}n(j,"promiseCall");const U=16384,bn=class bn{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){const r=this._back;let s=r;r._elements.length===U-1&&(s={_elements:[],_next:void 0}),r._elements.push(t),s!==r&&(this._back=s,r._next=s),++this._size}shift(){const t=this._front;let r=t;const s=this._cursor;let u=s+1;const c=t._elements,d=c[s];return u===U&&(r=t._next,u=0),--this._size,this._cursor=u,t!==r&&(this._front=r),c[s]=void 0,d}forEach(t){let r=this._cursor,s=this._front,u=s._elements;for(;(r!==u.length||s._next!==void 0)&&!(r===u.length&&(s=s._next,u=s._elements,r=0,u.length===0));)t(u[r]),++r}peek(){const t=this._front,r=this._cursor;return t._elements[r]}};n(bn,"SimpleQueue");let D=bn;const jt=Symbol("[[AbortSteps]]"),Qn=Symbol("[[ErrorSteps]]"),Ar=Symbol("[[CancelSteps]]"),Br=Symbol("[[PullSteps]]"),kr=Symbol("[[ReleaseSteps]]");function Yn(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?qr(e):t._state==="closed"?xi(e):Gn(e,t._storedError)}n(Yn,"ReadableStreamReaderGenericInitialize");function Wr(e,t){const r=e._ownerReadableStream;return ie(r,t)}n(Wr,"ReadableStreamReaderGenericCancel");function _e(e){const t=e._ownerReadableStream;t._state==="readable"?Or(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):Ni(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[kr](),t._reader=void 0,e._ownerReadableStream=void 0}n(_e,"ReadableStreamReaderGenericRelease");function Lt(e){return new TypeError("Cannot "+e+" a stream using a released reader")}n(Lt,"readerLockException");function qr(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}n(qr,"defaultReaderClosedPromiseInitialize");function Gn(e,t){qr(e),Or(e,t)}n(Gn,"defaultReaderClosedPromiseInitializeAsRejected");function xi(e){qr(e),Zn(e)}n(xi,"defaultReaderClosedPromiseInitializeAsResolved");function Or(e,t){e._closedPromise_reject!==void 0&&(Q(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(Or,"defaultReaderClosedPromiseReject");function Ni(e,t){Gn(e,t)}n(Ni,"defaultReaderClosedPromiseResetToRejected");function Zn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(Zn,"defaultReaderClosedPromiseResolve");const Kn=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},Hi=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function Vi(e){return typeof e=="object"||typeof e=="function"}n(Vi,"isDictionary");function ue(e,t){if(e!==void 0&&!Vi(e))throw new TypeError(`${t} is not an object.`)}n(ue,"assertDictionary");function Z(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}n(Z,"assertFunction");function Qi(e){return typeof e=="object"&&e!==null||typeof e=="function"}n(Qi,"isObject");function Jn(e,t){if(!Qi(e))throw new TypeError(`${t} is not an object.`)}n(Jn,"assertObject");function Se(e,t,r){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}n(Se,"assertRequiredArgument");function zr(e,t,r){if(e===void 0)throw new TypeError(`${t} is required in '${r}'.`)}n(zr,"assertRequiredField");function Ir(e){return Number(e)}n(Ir,"convertUnrestrictedDouble");function Xn(e){return e===0?0:e}n(Xn,"censorNegativeZero");function Yi(e){return Xn(Hi(e))}n(Yi,"integerPart");function Fr(e,t){const s=Number.MAX_SAFE_INTEGER;let u=Number(e);if(u=Xn(u),!Kn(u))throw new TypeError(`${t} is not a finite number`);if(u=Yi(u),u<0||u>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!Kn(u)||u===0?0:u}n(Fr,"convertUnsignedLongLongWithEnforceRange");function jr(e,t){if(!We(e))throw new TypeError(`${t} is not a ReadableStream.`)}n(jr,"assertReadableStream");function Qe(e){return new fe(e)}n(Qe,"AcquireReadableStreamDefaultReader");function eo(e,t){e._reader._readRequests.push(t)}n(eo,"ReadableStreamAddReadRequest");function Lr(e,t,r){const u=e._reader._readRequests.shift();r?u._closeSteps():u._chunkSteps(t)}n(Lr,"ReadableStreamFulfillReadRequest");function $t(e){return e._reader._readRequests.length}n($t,"ReadableStreamGetNumReadRequests");function to(e){const t=e._reader;return!(t===void 0||!Ee(t))}n(to,"ReadableStreamHasDefaultReader");const mn=class mn{constructor(t){if(Se(t,1,"ReadableStreamDefaultReader"),jr(t,"First parameter"),qe(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Yn(this,t),this._readRequests=new D}get closed(){return Ee(this)?this._closedPromise:b(Dt("closed"))}cancel(t=void 0){return Ee(this)?this._ownerReadableStream===void 0?b(Lt("cancel")):Wr(this,t):b(Dt("cancel"))}read(){if(!Ee(this))return b(Dt("read"));if(this._ownerReadableStream===void 0)return b(Lt("read from"));let t,r;const s=A((c,d)=>{t=c,r=d});return _t(this,{_chunkSteps:n(c=>t({value:c,done:!1}),"_chunkSteps"),_closeSteps:n(()=>t({value:void 0,done:!0}),"_closeSteps"),_errorSteps:n(c=>r(c),"_errorSteps")}),s}releaseLock(){if(!Ee(this))throw Dt("releaseLock");this._ownerReadableStream!==void 0&&Gi(this)}};n(mn,"ReadableStreamDefaultReader");let fe=mn;Object.defineProperties(fe.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),h(fe.prototype.cancel,"cancel"),h(fe.prototype.read,"read"),h(fe.prototype.releaseLock,"releaseLock"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(fe.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Ee(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof fe}n(Ee,"IsReadableStreamDefaultReader");function _t(e,t){const r=e._ownerReadableStream;r._disturbed=!0,r._state==="closed"?t._closeSteps():r._state==="errored"?t._errorSteps(r._storedError):r._readableStreamController[Br](t)}n(_t,"ReadableStreamDefaultReaderRead");function Gi(e){_e(e);const t=new TypeError("Reader was released");ro(e,t)}n(Gi,"ReadableStreamDefaultReaderRelease");function ro(e,t){const r=e._readRequests;e._readRequests=new D,r.forEach(s=>{s._errorSteps(t)})}n(ro,"ReadableStreamDefaultReaderErrorReadRequests");function Dt(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}n(Dt,"defaultReaderBrandCheckException");const Zi=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),yn=class yn{constructor(t,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=r}next(){const t=n(()=>this._nextSteps(),"nextSteps");return this._ongoingPromise=this._ongoingPromise?F(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){const r=n(()=>this._returnSteps(t),"returnSteps");return this._ongoingPromise?F(this._ongoingPromise,r,r):r()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const t=this._reader;let r,s;const u=A((d,m)=>{r=d,s=m});return _t(t,{_chunkSteps:n(d=>{this._ongoingPromise=void 0,ge(()=>r({value:d,done:!1}))},"_chunkSteps"),_closeSteps:n(()=>{this._ongoingPromise=void 0,this._isFinished=!0,_e(t),r({value:void 0,done:!0})},"_closeSteps"),_errorSteps:n(d=>{this._ongoingPromise=void 0,this._isFinished=!0,_e(t),s(d)},"_errorSteps")}),u}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;const r=this._reader;if(!this._preventCancel){const s=Wr(r,t);return _e(r),F(s,()=>({value:t,done:!0}))}return _e(r),T({value:t,done:!0})}};n(yn,"ReadableStreamAsyncIteratorImpl");let Mt=yn;const no={next(){return oo(this)?this._asyncIteratorImpl.next():b(io("next"))},return(e){return oo(this)?this._asyncIteratorImpl.return(e):b(io("return"))}};Object.setPrototypeOf(no,Zi);function Ki(e,t){const r=Qe(e),s=new Mt(r,t),u=Object.create(no);return u._asyncIteratorImpl=s,u}n(Ki,"AcquireReadableStreamAsyncIterator");function oo(e){if(!l(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Mt}catch{return!1}}n(oo,"IsReadableStreamAsyncIterator");function io(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}n(io,"streamAsyncIteratorBrandCheckException");const ao=Number.isNaN||function(e){return e!==e};var $r,Dr,Mr;function St(e){return e.slice()}n(St,"CreateArrayFromList");function so(e,t,r,s,u){new Uint8Array(e).set(new Uint8Array(r,s,u),t)}n(so,"CopyDataBlockBytes");let we=n(e=>(typeof e.transfer=="function"?we=n(t=>t.transfer(),"TransferArrayBuffer"):typeof structuredClone=="function"?we=n(t=>structuredClone(t,{transfer:[t]}),"TransferArrayBuffer"):we=n(t=>t,"TransferArrayBuffer"),we(e)),"TransferArrayBuffer"),Ae=n(e=>(typeof e.detached=="boolean"?Ae=n(t=>t.detached,"IsDetachedBuffer"):Ae=n(t=>t.byteLength===0,"IsDetachedBuffer"),Ae(e)),"IsDetachedBuffer");function lo(e,t,r){if(e.slice)return e.slice(t,r);const s=r-t,u=new ArrayBuffer(s);return so(u,0,e,t,s),u}n(lo,"ArrayBufferSlice");function Ut(e,t){const r=e[t];if(r!=null){if(typeof r!="function")throw new TypeError(`${String(t)} is not a function`);return r}}n(Ut,"GetMethod");function Ji(e){const t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),s=r.next;return{iterator:r,nextMethod:s,done:!1}}n(Ji,"CreateAsyncFromSyncIterator");const Ur=(Mr=($r=Symbol.asyncIterator)!==null&&$r!==void 0?$r:(Dr=Symbol.for)===null||Dr===void 0?void 0:Dr.call(Symbol,"Symbol.asyncIterator"))!==null&&Mr!==void 0?Mr:"@@asyncIterator";function uo(e,t="sync",r){if(r===void 0)if(t==="async"){if(r=Ut(e,Ur),r===void 0){const c=Ut(e,Symbol.iterator),d=uo(e,"sync",c);return Ji(d)}}else r=Ut(e,Symbol.iterator);if(r===void 0)throw new TypeError("The object is not iterable");const s=z(r,e,[]);if(!l(s))throw new TypeError("The iterator method must return an object");const u=s.next;return{iterator:s,nextMethod:u,done:!1}}n(uo,"GetIterator");function Xi(e){const t=z(e.nextMethod,e.iterator,[]);if(!l(t))throw new TypeError("The iterator.next() method must return an object");return t}n(Xi,"IteratorNext");function ea(e){return!!e.done}n(ea,"IteratorComplete");function ta(e){return e.value}n(ta,"IteratorValue");function ra(e){return!(typeof e!="number"||ao(e)||e<0)}n(ra,"IsNonNegativeNumber");function fo(e){const t=lo(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}n(fo,"CloneAsUint8Array");function xr(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}n(xr,"DequeueValue");function Nr(e,t,r){if(!ra(r)||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}n(Nr,"EnqueueValueWithSize");function na(e){return e._queue.peek().value}n(na,"PeekQueueValue");function Be(e){e._queue=new D,e._queueTotalSize=0}n(Be,"ResetQueue");function co(e){return e===DataView}n(co,"isDataViewConstructor");function oa(e){return co(e.constructor)}n(oa,"isDataView");function ia(e){return co(e)?1:e.BYTES_PER_ELEMENT}n(ia,"arrayBufferViewElementSize");const gn=class gn{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Hr(this))throw Zr("view");return this._view}respond(t){if(!Hr(this))throw Zr("respond");if(Se(t,1,"respond"),t=Fr(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(Ae(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Vt(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!Hr(this))throw Zr("respondWithNewView");if(Se(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(Ae(t.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Qt(this._associatedReadableByteStreamController,t)}};n(gn,"ReadableStreamBYOBRequest");let Re=gn;Object.defineProperties(Re.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),h(Re.prototype.respond,"respond"),h(Re.prototype.respondWithNewView,"respondWithNewView"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(Re.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});const _n=class _n{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Ie(this))throw Rt("byobRequest");return Gr(this)}get desiredSize(){if(!Ie(this))throw Rt("desiredSize");return Ro(this)}close(){if(!Ie(this))throw Rt("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);wt(this)}enqueue(t){if(!Ie(this))throw Rt("enqueue");if(Se(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const r=this._controlledReadableByteStream._state;if(r!=="readable")throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be enqueued to`);Ht(this,t)}error(t=void 0){if(!Ie(this))throw Rt("error");K(this,t)}[Ar](t){ho(this),Be(this);const r=this._cancelAlgorithm(t);return Nt(this),r}[Br](t){const r=this._controlledReadableByteStream;if(this._queueTotalSize>0){wo(this,t);return}const s=this._autoAllocateChunkSize;if(s!==void 0){let u;try{u=new ArrayBuffer(s)}catch(d){t._errorSteps(d);return}const c={buffer:u,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(c)}eo(r,t),Fe(this)}[kr](){if(this._pendingPullIntos.length>0){const t=this._pendingPullIntos.peek();t.readerType="none",this._pendingPullIntos=new D,this._pendingPullIntos.push(t)}}};n(_n,"ReadableByteStreamController");let te=_n;Object.defineProperties(te.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),h(te.prototype.close,"close"),h(te.prototype.enqueue,"enqueue"),h(te.prototype.error,"error"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(te.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function Ie(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof te}n(Ie,"IsReadableByteStreamController");function Hr(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof Re}n(Hr,"IsReadableStreamBYOBRequest");function Fe(e){if(!fa(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();g(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Fe(e)),null),s=>(K(e,s),null))}n(Fe,"ReadableByteStreamControllerCallPullIfNeeded");function ho(e){Qr(e),e._pendingPullIntos=new D}n(ho,"ReadableByteStreamControllerClearPendingPullIntos");function Vr(e,t){let r=!1;e._state==="closed"&&(r=!0);const s=po(t);t.readerType==="default"?Lr(e,s,r):ma(e,s,r)}n(Vr,"ReadableByteStreamControllerCommitPullIntoDescriptor");function po(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}n(po,"ReadableByteStreamControllerConvertPullIntoDescriptor");function xt(e,t,r,s){e._queue.push({buffer:t,byteOffset:r,byteLength:s}),e._queueTotalSize+=s}n(xt,"ReadableByteStreamControllerEnqueueChunkToQueue");function bo(e,t,r,s){let u;try{u=lo(t,r,r+s)}catch(c){throw K(e,c),c}xt(e,u,0,s)}n(bo,"ReadableByteStreamControllerEnqueueClonedChunkToQueue");function mo(e,t){t.bytesFilled>0&&bo(e,t.buffer,t.byteOffset,t.bytesFilled),Ye(e)}n(mo,"ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue");function yo(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),s=t.bytesFilled+r;let u=r,c=!1;const d=s%t.elementSize,m=s-d;m>=t.minimumFill&&(u=m-t.bytesFilled,c=!0);const R=e._queue;for(;u>0;){const y=R.peek(),C=Math.min(u,y.byteLength),P=t.byteOffset+t.bytesFilled;so(t.buffer,P,y.buffer,y.byteOffset,C),y.byteLength===C?R.shift():(y.byteOffset+=C,y.byteLength-=C),e._queueTotalSize-=C,go(e,C,t),u-=C}return c}n(yo,"ReadableByteStreamControllerFillPullIntoDescriptorFromQueue");function go(e,t,r){r.bytesFilled+=t}n(go,"ReadableByteStreamControllerFillHeadPullIntoDescriptor");function _o(e){e._queueTotalSize===0&&e._closeRequested?(Nt(e),At(e._controlledReadableByteStream)):Fe(e)}n(_o,"ReadableByteStreamControllerHandleQueueDrain");function Qr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}n(Qr,"ReadableByteStreamControllerInvalidateBYOBRequest");function Yr(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;const t=e._pendingPullIntos.peek();yo(e,t)&&(Ye(e),Vr(e._controlledReadableByteStream,t))}}n(Yr,"ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue");function aa(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(e._queueTotalSize===0)return;const r=t._readRequests.shift();wo(e,r)}}n(aa,"ReadableByteStreamControllerProcessReadRequestsUsingQueue");function sa(e,t,r,s){const u=e._controlledReadableByteStream,c=t.constructor,d=ia(c),{byteOffset:m,byteLength:R}=t,y=r*d;let C;try{C=we(t.buffer)}catch(B){s._errorSteps(B);return}const P={buffer:C,bufferByteLength:C.byteLength,byteOffset:m,byteLength:R,bytesFilled:0,minimumFill:y,elementSize:d,viewConstructor:c,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(P),Po(u,s);return}if(u._state==="closed"){const B=new c(P.buffer,P.byteOffset,0);s._closeSteps(B);return}if(e._queueTotalSize>0){if(yo(e,P)){const B=po(P);_o(e),s._chunkSteps(B);return}if(e._closeRequested){const B=new TypeError("Insufficient bytes to fill elements in the given buffer");K(e,B),s._errorSteps(B);return}}e._pendingPullIntos.push(P),Po(u,s),Fe(e)}n(sa,"ReadableByteStreamControllerPullInto");function la(e,t){t.readerType==="none"&&Ye(e);const r=e._controlledReadableByteStream;if(Kr(r))for(;vo(r)>0;){const s=Ye(e);Vr(r,s)}}n(la,"ReadableByteStreamControllerRespondInClosedState");function ua(e,t,r){if(go(e,t,r),r.readerType==="none"){mo(e,r),Yr(e);return}if(r.bytesFilled<r.minimumFill)return;Ye(e);const s=r.bytesFilled%r.elementSize;if(s>0){const u=r.byteOffset+r.bytesFilled;bo(e,r.buffer,u-s,s)}r.bytesFilled-=s,Vr(e._controlledReadableByteStream,r),Yr(e)}n(ua,"ReadableByteStreamControllerRespondInReadableState");function So(e,t){const r=e._pendingPullIntos.peek();Qr(e),e._controlledReadableByteStream._state==="closed"?la(e,r):ua(e,t,r),Fe(e)}n(So,"ReadableByteStreamControllerRespondInternal");function Ye(e){return e._pendingPullIntos.shift()}n(Ye,"ReadableByteStreamControllerShiftPendingPullInto");function fa(e){const t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(to(t)&&$t(t)>0||Kr(t)&&vo(t)>0||Ro(e)>0)}n(fa,"ReadableByteStreamControllerShouldCallPull");function Nt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}n(Nt,"ReadableByteStreamControllerClearAlgorithms");function wt(e){const t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){const r=e._pendingPullIntos.peek();if(r.bytesFilled%r.elementSize!==0){const s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw K(e,s),s}}Nt(e),At(t)}}n(wt,"ReadableByteStreamControllerClose");function Ht(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||r._state!=="readable")return;const{buffer:s,byteOffset:u,byteLength:c}=t;if(Ae(s))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const d=we(s);if(e._pendingPullIntos.length>0){const m=e._pendingPullIntos.peek();if(Ae(m.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Qr(e),m.buffer=we(m.buffer),m.readerType==="none"&&mo(e,m)}if(to(r))if(aa(e),$t(r)===0)xt(e,d,u,c);else{e._pendingPullIntos.length>0&&Ye(e);const m=new Uint8Array(d,u,c);Lr(r,m,!1)}else Kr(r)?(xt(e,d,u,c),Yr(e)):xt(e,d,u,c);Fe(e)}n(Ht,"ReadableByteStreamControllerEnqueue");function K(e,t){const r=e._controlledReadableByteStream;r._state==="readable"&&(ho(e),Be(e),Nt(e),Zo(r,t))}n(K,"ReadableByteStreamControllerError");function wo(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,_o(e);const s=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(s)}n(wo,"ReadableByteStreamControllerFillReadRequestFromQueue");function Gr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(Re.prototype);da(s,e,r),e._byobRequest=s}return e._byobRequest}n(Gr,"ReadableByteStreamControllerGetBYOBRequest");function Ro(e){const t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}n(Ro,"ReadableByteStreamControllerGetDesiredSize");function Vt(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=we(r.buffer),So(e,t)}n(Vt,"ReadableByteStreamControllerRespond");function Qt(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const u=t.byteLength;r.buffer=we(t.buffer),So(e,u)}n(Qt,"ReadableByteStreamControllerRespondWithNewView");function To(e,t,r,s,u,c,d){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Be(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=u,t._autoAllocateChunkSize=d,t._pendingPullIntos=new D,e._readableStreamController=t;const m=r();g(T(m),()=>(t._started=!0,Fe(t),null),R=>(K(t,R),null))}n(To,"SetUpReadableByteStreamController");function ca(e,t,r){const s=Object.create(te.prototype);let u,c,d;t.start!==void 0?u=n(()=>t.start(s),"startAlgorithm"):u=n(()=>{},"startAlgorithm"),t.pull!==void 0?c=n(()=>t.pull(s),"pullAlgorithm"):c=n(()=>T(void 0),"pullAlgorithm"),t.cancel!==void 0?d=n(R=>t.cancel(R),"cancelAlgorithm"):d=n(()=>T(void 0),"cancelAlgorithm");const m=t.autoAllocateChunkSize;if(m===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");To(e,s,u,c,d,r,m)}n(ca,"SetUpReadableByteStreamControllerFromUnderlyingSource");function da(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}n(da,"SetUpReadableStreamBYOBRequest");function Zr(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}n(Zr,"byobRequestBrandCheckException");function Rt(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}n(Rt,"byteStreamControllerBrandCheckException");function ha(e,t){ue(e,t);const r=e?.mode;return{mode:r===void 0?void 0:pa(r,`${t} has member 'mode' that`)}}n(ha,"convertReaderOptions");function pa(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}n(pa,"convertReadableStreamReaderMode");function ba(e,t){var r;ue(e,t);const s=(r=e?.min)!==null&&r!==void 0?r:1;return{min:Fr(s,`${t} has member 'min' that`)}}n(ba,"convertByobReadOptions");function Co(e){return new ce(e)}n(Co,"AcquireReadableStreamBYOBReader");function Po(e,t){e._reader._readIntoRequests.push(t)}n(Po,"ReadableStreamAddReadIntoRequest");function ma(e,t,r){const u=e._reader._readIntoRequests.shift();r?u._closeSteps(t):u._chunkSteps(t)}n(ma,"ReadableStreamFulfillReadIntoRequest");function vo(e){return e._reader._readIntoRequests.length}n(vo,"ReadableStreamGetNumReadIntoRequests");function Kr(e){const t=e._reader;return!(t===void 0||!je(t))}n(Kr,"ReadableStreamHasBYOBReader");const Sn=class Sn{constructor(t){if(Se(t,1,"ReadableStreamBYOBReader"),jr(t,"First parameter"),qe(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Ie(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Yn(this,t),this._readIntoRequests=new D}get closed(){return je(this)?this._closedPromise:b(Yt("closed"))}cancel(t=void 0){return je(this)?this._ownerReadableStream===void 0?b(Lt("cancel")):Wr(this,t):b(Yt("cancel"))}read(t,r={}){if(!je(this))return b(Yt("read"));if(!ArrayBuffer.isView(t))return b(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return b(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return b(new TypeError("view's buffer must have non-zero byteLength"));if(Ae(t.buffer))return b(new TypeError("view's buffer has been detached"));let s;try{s=ba(r,"options")}catch(y){return b(y)}const u=s.min;if(u===0)return b(new TypeError("options.min must be greater than 0"));if(oa(t)){if(u>t.byteLength)return b(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(u>t.length)return b(new RangeError("options.min must be less than or equal to view's length"));if(this._ownerReadableStream===void 0)return b(Lt("read from"));let c,d;const m=A((y,C)=>{c=y,d=C});return Eo(this,t,u,{_chunkSteps:n(y=>c({value:y,done:!1}),"_chunkSteps"),_closeSteps:n(y=>c({value:y,done:!0}),"_closeSteps"),_errorSteps:n(y=>d(y),"_errorSteps")}),m}releaseLock(){if(!je(this))throw Yt("releaseLock");this._ownerReadableStream!==void 0&&ya(this)}};n(Sn,"ReadableStreamBYOBReader");let ce=Sn;Object.defineProperties(ce.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),h(ce.prototype.cancel,"cancel"),h(ce.prototype.read,"read"),h(ce.prototype.releaseLock,"releaseLock"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ce.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function je(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof ce}n(je,"IsReadableStreamBYOBReader");function Eo(e,t,r,s){const u=e._ownerReadableStream;u._disturbed=!0,u._state==="errored"?s._errorSteps(u._storedError):sa(u._readableStreamController,t,r,s)}n(Eo,"ReadableStreamBYOBReaderRead");function ya(e){_e(e);const t=new TypeError("Reader was released");Ao(e,t)}n(ya,"ReadableStreamBYOBReaderRelease");function Ao(e,t){const r=e._readIntoRequests;e._readIntoRequests=new D,r.forEach(s=>{s._errorSteps(t)})}n(Ao,"ReadableStreamBYOBReaderErrorReadIntoRequests");function Yt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}n(Yt,"byobReaderBrandCheckException");function Tt(e,t){const{highWaterMark:r}=e;if(r===void 0)return t;if(ao(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}n(Tt,"ExtractHighWaterMark");function Gt(e){const{size:t}=e;return t||(()=>1)}n(Gt,"ExtractSizeAlgorithm");function Zt(e,t){ue(e,t);const r=e?.highWaterMark,s=e?.size;return{highWaterMark:r===void 0?void 0:Ir(r),size:s===void 0?void 0:ga(s,`${t} has member 'size' that`)}}n(Zt,"convertQueuingStrategy");function ga(e,t){return Z(e,t),r=>Ir(e(r))}n(ga,"convertQueuingStrategySize");function _a(e,t){ue(e,t);const r=e?.abort,s=e?.close,u=e?.start,c=e?.type,d=e?.write;return{abort:r===void 0?void 0:Sa(r,e,`${t} has member 'abort' that`),close:s===void 0?void 0:wa(s,e,`${t} has member 'close' that`),start:u===void 0?void 0:Ra(u,e,`${t} has member 'start' that`),write:d===void 0?void 0:Ta(d,e,`${t} has member 'write' that`),type:c}}n(_a,"convertUnderlyingSink");function Sa(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(Sa,"convertUnderlyingSinkAbortCallback");function wa(e,t,r){return Z(e,r),()=>j(e,t,[])}n(wa,"convertUnderlyingSinkCloseCallback");function Ra(e,t,r){return Z(e,r),s=>z(e,t,[s])}n(Ra,"convertUnderlyingSinkStartCallback");function Ta(e,t,r){return Z(e,r),(s,u)=>j(e,t,[s,u])}n(Ta,"convertUnderlyingSinkWriteCallback");function Bo(e,t){if(!Ge(e))throw new TypeError(`${t} is not a WritableStream.`)}n(Bo,"assertWritableStream");function Ca(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}n(Ca,"isAbortSignal");const Pa=typeof AbortController=="function";function va(){if(Pa)return new AbortController}n(va,"createAbortController");const wn=class wn{constructor(t={},r={}){t===void 0?t=null:Jn(t,"First parameter");const s=Zt(r,"Second parameter"),u=_a(t,"First parameter");if(Wo(this),u.type!==void 0)throw new RangeError("Invalid type is specified");const d=Gt(s),m=Tt(s,1);Da(this,u,m,d)}get locked(){if(!Ge(this))throw tr("locked");return Ze(this)}abort(t=void 0){return Ge(this)?Ze(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Kt(this,t):b(tr("abort"))}close(){return Ge(this)?Ze(this)?b(new TypeError("Cannot close a stream that already has a writer")):he(this)?b(new TypeError("Cannot close an already-closing stream")):qo(this):b(tr("close"))}getWriter(){if(!Ge(this))throw tr("getWriter");return ko(this)}};n(wn,"WritableStream");let de=wn;Object.defineProperties(de.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),h(de.prototype.abort,"abort"),h(de.prototype.close,"close"),h(de.prototype.getWriter,"getWriter"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(de.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});function ko(e){return new re(e)}n(ko,"AcquireWritableStreamDefaultWriter");function Ea(e,t,r,s,u=1,c=()=>1){const d=Object.create(de.prototype);Wo(d);const m=Object.create(ke.prototype);return Lo(d,m,e,t,r,s,u,c),d}n(Ea,"CreateWritableStream");function Wo(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new D,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}n(Wo,"InitializeWritableStream");function Ge(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof de}n(Ge,"IsWritableStream");function Ze(e){return e._writer!==void 0}n(Ze,"IsWritableStreamLocked");function Kt(e,t){var r;if(e._state==="closed"||e._state==="errored")return T(void 0);e._writableStreamController._abortReason=t,(r=e._writableStreamController._abortController)===null||r===void 0||r.abort(t);const s=e._state;if(s==="closed"||s==="errored")return T(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let u=!1;s==="erroring"&&(u=!0,t=void 0);const c=A((d,m)=>{e._pendingAbortRequest={_promise:void 0,_resolve:d,_reject:m,_reason:t,_wasAlreadyErroring:u}});return e._pendingAbortRequest._promise=c,u||Xr(e,t),c}n(Kt,"WritableStreamAbort");function qo(e){const t=e._state;if(t==="closed"||t==="errored")return b(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=A((u,c)=>{const d={_resolve:u,_reject:c};e._closeRequest=d}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&ln(s),Ma(e._writableStreamController),r}n(qo,"WritableStreamClose");function Aa(e){return A((r,s)=>{const u={_resolve:r,_reject:s};e._writeRequests.push(u)})}n(Aa,"WritableStreamAddWriteRequest");function Jr(e,t){if(e._state==="writable"){Xr(e,t);return}en(e)}n(Jr,"WritableStreamDealWithRejection");function Xr(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const s=e._writer;s!==void 0&&zo(s,t),!Oa(e)&&r._started&&en(e)}n(Xr,"WritableStreamStartErroring");function en(e){e._state="errored",e._writableStreamController[Qn]();const t=e._storedError;if(e._writeRequests.forEach(u=>{u._reject(t)}),e._writeRequests=new D,e._pendingAbortRequest===void 0){Jt(e);return}const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),Jt(e);return}const s=e._writableStreamController[jt](r._reason);g(s,()=>(r._resolve(),Jt(e),null),u=>(r._reject(u),Jt(e),null))}n(en,"WritableStreamFinishErroring");function Ba(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}n(Ba,"WritableStreamFinishInFlightWrite");function ka(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Jr(e,t)}n(ka,"WritableStreamFinishInFlightWriteWithError");function Wa(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const r=e._writer;r!==void 0&&Uo(r)}n(Wa,"WritableStreamFinishInFlightClose");function qa(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Jr(e,t)}n(qa,"WritableStreamFinishInFlightCloseWithError");function he(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}n(he,"WritableStreamCloseQueuedOrInFlight");function Oa(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}n(Oa,"WritableStreamHasOperationMarkedInFlight");function za(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}n(za,"WritableStreamMarkCloseRequestInFlight");function Ia(e){e._inFlightWriteRequest=e._writeRequests.shift()}n(Ia,"WritableStreamMarkFirstWriteRequestInFlight");function Jt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;t!==void 0&&an(t,e._storedError)}n(Jt,"WritableStreamRejectCloseAndClosedPromiseIfNeeded");function tn(e,t){const r=e._writer;r!==void 0&&t!==e._backpressure&&(t?Ya(r):ln(r)),e._backpressure=t}n(tn,"WritableStreamUpdateBackpressure");const Rn=class Rn{constructor(t){if(Se(t,1,"WritableStreamDefaultWriter"),Bo(t,"First parameter"),Ze(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;const r=t._state;if(r==="writable")!he(t)&&t._backpressure?nr(this):xo(this),rr(this);else if(r==="erroring")sn(this,t._storedError),rr(this);else if(r==="closed")xo(this),Va(this);else{const s=t._storedError;sn(this,s),Mo(this,s)}}get closed(){return Le(this)?this._closedPromise:b($e("closed"))}get desiredSize(){if(!Le(this))throw $e("desiredSize");if(this._ownerWritableStream===void 0)throw Pt("desiredSize");return $a(this)}get ready(){return Le(this)?this._readyPromise:b($e("ready"))}abort(t=void 0){return Le(this)?this._ownerWritableStream===void 0?b(Pt("abort")):Fa(this,t):b($e("abort"))}close(){if(!Le(this))return b($e("close"));const t=this._ownerWritableStream;return t===void 0?b(Pt("close")):he(t)?b(new TypeError("Cannot close an already-closing stream")):Oo(this)}releaseLock(){if(!Le(this))throw $e("releaseLock");this._ownerWritableStream!==void 0&&Io(this)}write(t=void 0){return Le(this)?this._ownerWritableStream===void 0?b(Pt("write to")):Fo(this,t):b($e("write"))}};n(Rn,"WritableStreamDefaultWriter");let re=Rn;Object.defineProperties(re.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),h(re.prototype.abort,"abort"),h(re.prototype.close,"close"),h(re.prototype.releaseLock,"releaseLock"),h(re.prototype.write,"write"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(re.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function Le(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof re}n(Le,"IsWritableStreamDefaultWriter");function Fa(e,t){const r=e._ownerWritableStream;return Kt(r,t)}n(Fa,"WritableStreamDefaultWriterAbort");function Oo(e){const t=e._ownerWritableStream;return qo(t)}n(Oo,"WritableStreamDefaultWriterClose");function ja(e){const t=e._ownerWritableStream,r=t._state;return he(t)||r==="closed"?T(void 0):r==="errored"?b(t._storedError):Oo(e)}n(ja,"WritableStreamDefaultWriterCloseWithErrorPropagation");function La(e,t){e._closedPromiseState==="pending"?an(e,t):Qa(e,t)}n(La,"WritableStreamDefaultWriterEnsureClosedPromiseRejected");function zo(e,t){e._readyPromiseState==="pending"?No(e,t):Ga(e,t)}n(zo,"WritableStreamDefaultWriterEnsureReadyPromiseRejected");function $a(e){const t=e._ownerWritableStream,r=t._state;return r==="errored"||r==="erroring"?null:r==="closed"?0:$o(t._writableStreamController)}n($a,"WritableStreamDefaultWriterGetDesiredSize");function Io(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");zo(e,r),La(e,r),t._writer=void 0,e._ownerWritableStream=void 0}n(Io,"WritableStreamDefaultWriterRelease");function Fo(e,t){const r=e._ownerWritableStream,s=r._writableStreamController,u=Ua(s,t);if(r!==e._ownerWritableStream)return b(Pt("write to"));const c=r._state;if(c==="errored")return b(r._storedError);if(he(r)||c==="closed")return b(new TypeError("The stream is closing or closed and cannot be written to"));if(c==="erroring")return b(r._storedError);const d=Aa(r);return xa(s,t,u),d}n(Fo,"WritableStreamDefaultWriterWrite");const jo={},Tn=class Tn{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!rn(this))throw on("abortReason");return this._abortReason}get signal(){if(!rn(this))throw on("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!rn(this))throw on("error");this._controlledWritableStream._state==="writable"&&Do(this,t)}[jt](t){const r=this._abortAlgorithm(t);return Xt(this),r}[Qn](){Be(this)}};n(Tn,"WritableStreamDefaultController");let ke=Tn;Object.defineProperties(ke.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ke.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function rn(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof ke}n(rn,"IsWritableStreamDefaultController");function Lo(e,t,r,s,u,c,d,m){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Be(t),t._abortReason=void 0,t._abortController=va(),t._started=!1,t._strategySizeAlgorithm=m,t._strategyHWM=d,t._writeAlgorithm=s,t._closeAlgorithm=u,t._abortAlgorithm=c;const R=nn(t);tn(e,R);const y=r(),C=T(y);g(C,()=>(t._started=!0,er(t),null),P=>(t._started=!0,Jr(e,P),null))}n(Lo,"SetUpWritableStreamDefaultController");function Da(e,t,r,s){const u=Object.create(ke.prototype);let c,d,m,R;t.start!==void 0?c=n(()=>t.start(u),"startAlgorithm"):c=n(()=>{},"startAlgorithm"),t.write!==void 0?d=n(y=>t.write(y,u),"writeAlgorithm"):d=n(()=>T(void 0),"writeAlgorithm"),t.close!==void 0?m=n(()=>t.close(),"closeAlgorithm"):m=n(()=>T(void 0),"closeAlgorithm"),t.abort!==void 0?R=n(y=>t.abort(y),"abortAlgorithm"):R=n(()=>T(void 0),"abortAlgorithm"),Lo(e,u,c,d,m,R,r,s)}n(Da,"SetUpWritableStreamDefaultControllerFromUnderlyingSink");function Xt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(Xt,"WritableStreamDefaultControllerClearAlgorithms");function Ma(e){Nr(e,jo,0),er(e)}n(Ma,"WritableStreamDefaultControllerClose");function Ua(e,t){try{return e._strategySizeAlgorithm(t)}catch(r){return Ct(e,r),1}}n(Ua,"WritableStreamDefaultControllerGetChunkSize");function $o(e){return e._strategyHWM-e._queueTotalSize}n($o,"WritableStreamDefaultControllerGetDesiredSize");function xa(e,t,r){try{Nr(e,t,r)}catch(u){Ct(e,u);return}const s=e._controlledWritableStream;if(!he(s)&&s._state==="writable"){const u=nn(e);tn(s,u)}er(e)}n(xa,"WritableStreamDefaultControllerWrite");function er(e){const t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){en(t);return}if(e._queue.length===0)return;const s=na(e);s===jo?Na(e):Ha(e,s)}n(er,"WritableStreamDefaultControllerAdvanceQueueIfNeeded");function Ct(e,t){e._controlledWritableStream._state==="writable"&&Do(e,t)}n(Ct,"WritableStreamDefaultControllerErrorIfNeeded");function Na(e){const t=e._controlledWritableStream;za(t),xr(e);const r=e._closeAlgorithm();Xt(e),g(r,()=>(Wa(t),null),s=>(qa(t,s),null))}n(Na,"WritableStreamDefaultControllerProcessClose");function Ha(e,t){const r=e._controlledWritableStream;Ia(r);const s=e._writeAlgorithm(t);g(s,()=>{Ba(r);const u=r._state;if(xr(e),!he(r)&&u==="writable"){const c=nn(e);tn(r,c)}return er(e),null},u=>(r._state==="writable"&&Xt(e),ka(r,u),null))}n(Ha,"WritableStreamDefaultControllerProcessWrite");function nn(e){return $o(e)<=0}n(nn,"WritableStreamDefaultControllerGetBackpressure");function Do(e,t){const r=e._controlledWritableStream;Xt(e),Xr(r,t)}n(Do,"WritableStreamDefaultControllerError");function tr(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}n(tr,"streamBrandCheckException$2");function on(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}n(on,"defaultControllerBrandCheckException$2");function $e(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}n($e,"defaultWriterBrandCheckException");function Pt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}n(Pt,"defaultWriterLockException");function rr(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}n(rr,"defaultWriterClosedPromiseInitialize");function Mo(e,t){rr(e),an(e,t)}n(Mo,"defaultWriterClosedPromiseInitializeAsRejected");function Va(e){rr(e),Uo(e)}n(Va,"defaultWriterClosedPromiseInitializeAsResolved");function an(e,t){e._closedPromise_reject!==void 0&&(Q(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}n(an,"defaultWriterClosedPromiseReject");function Qa(e,t){Mo(e,t)}n(Qa,"defaultWriterClosedPromiseResetToRejected");function Uo(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}n(Uo,"defaultWriterClosedPromiseResolve");function nr(e){e._readyPromise=A((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}n(nr,"defaultWriterReadyPromiseInitialize");function sn(e,t){nr(e),No(e,t)}n(sn,"defaultWriterReadyPromiseInitializeAsRejected");function xo(e){nr(e),ln(e)}n(xo,"defaultWriterReadyPromiseInitializeAsResolved");function No(e,t){e._readyPromise_reject!==void 0&&(Q(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}n(No,"defaultWriterReadyPromiseReject");function Ya(e){nr(e)}n(Ya,"defaultWriterReadyPromiseReset");function Ga(e,t){sn(e,t)}n(Ga,"defaultWriterReadyPromiseResetToRejected");function ln(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}n(ln,"defaultWriterReadyPromiseResolve");function Za(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof di<"u")return di}n(Za,"getGlobals");const un=Za();function Ka(e){if(!(typeof e=="function"||typeof e=="object")||e.name!=="DOMException")return!1;try{return new e,!0}catch{return!1}}n(Ka,"isDOMExceptionConstructor");function Ja(){const e=un?.DOMException;return Ka(e)?e:void 0}n(Ja,"getFromGlobal");function Xa(){const e=n(function(r,s){this.message=r||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)},"DOMException");return h(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}n(Xa,"createPolyfill");const es=Ja()||Xa();function Ho(e,t,r,s,u,c){const d=Qe(e),m=ko(t);e._disturbed=!0;let R=!1,y=T(void 0);return A((C,P)=>{let B;if(c!==void 0){if(B=n(()=>{const _=c.reason!==void 0?c.reason:new es("Aborted","AbortError"),E=[];s||E.push(()=>t._state==="writable"?Kt(t,_):T(void 0)),u||E.push(()=>e._state==="readable"?ie(e,_):T(void 0)),N(()=>Promise.all(E.map(k=>k())),!0,_)},"abortAlgorithm"),c.aborted){B();return}c.addEventListener("abort",B)}function ae(){return A((_,E)=>{function k(Y){Y?_():q(nt(),k,E)}n(k,"next"),k(!1)})}n(ae,"pipeLoop");function nt(){return R?T(!0):q(m._readyPromise,()=>A((_,E)=>{_t(d,{_chunkSteps:n(k=>{y=q(Fo(m,k),void 0,f),_(!1)},"_chunkSteps"),_closeSteps:n(()=>_(!0),"_closeSteps"),_errorSteps:E})}))}if(n(nt,"pipeStep"),Te(e,d._closedPromise,_=>(s?J(!0,_):N(()=>Kt(t,_),!0,_),null)),Te(t,m._closedPromise,_=>(u?J(!0,_):N(()=>ie(e,_),!0,_),null)),x(e,d._closedPromise,()=>(r?J():N(()=>ja(m)),null)),he(t)||t._state==="closed"){const _=new TypeError("the destination writable stream closed before all data could be piped to it");u?J(!0,_):N(()=>ie(e,_),!0,_)}Q(ae());function Oe(){const _=y;return q(y,()=>_!==y?Oe():void 0)}n(Oe,"waitForWritesToFinish");function Te(_,E,k){_._state==="errored"?k(_._storedError):I(E,k)}n(Te,"isOrBecomesErrored");function x(_,E,k){_._state==="closed"?k():V(E,k)}n(x,"isOrBecomesClosed");function N(_,E,k){if(R)return;R=!0,t._state==="writable"&&!he(t)?V(Oe(),Y):Y();function Y(){return g(_(),()=>Ce(E,k),ot=>Ce(!0,ot)),null}n(Y,"doTheRest")}n(N,"shutdownWithAction");function J(_,E){R||(R=!0,t._state==="writable"&&!he(t)?V(Oe(),()=>Ce(_,E)):Ce(_,E))}n(J,"shutdown");function Ce(_,E){return Io(m),_e(d),c!==void 0&&c.removeEventListener("abort",B),_?P(E):C(void 0),null}n(Ce,"finalize")})}n(Ho,"ReadableStreamPipeTo");const Cn=class Cn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!or(this))throw ar("desiredSize");return fn(this)}close(){if(!or(this))throw ar("close");if(!Je(this))throw new TypeError("The stream is not in a state that permits close");De(this)}enqueue(t=void 0){if(!or(this))throw ar("enqueue");if(!Je(this))throw new TypeError("The stream is not in a state that permits enqueue");return Ke(this,t)}error(t=void 0){if(!or(this))throw ar("error");oe(this,t)}[Ar](t){Be(this);const r=this._cancelAlgorithm(t);return ir(this),r}[Br](t){const r=this._controlledReadableStream;if(this._queue.length>0){const s=xr(this);this._closeRequested&&this._queue.length===0?(ir(this),At(r)):vt(this),t._chunkSteps(s)}else eo(r,t),vt(this)}[kr](){}};n(Cn,"ReadableStreamDefaultController");let ne=Cn;Object.defineProperties(ne.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),h(ne.prototype.close,"close"),h(ne.prototype.enqueue,"enqueue"),h(ne.prototype.error,"error"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ne.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function or(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof ne}n(or,"IsReadableStreamDefaultController");function vt(e){if(!Vo(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();g(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,vt(e)),null),s=>(oe(e,s),null))}n(vt,"ReadableStreamDefaultControllerCallPullIfNeeded");function Vo(e){const t=e._controlledReadableStream;return!Je(e)||!e._started?!1:!!(qe(t)&&$t(t)>0||fn(e)>0)}n(Vo,"ReadableStreamDefaultControllerShouldCallPull");function ir(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(ir,"ReadableStreamDefaultControllerClearAlgorithms");function De(e){if(!Je(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(ir(e),At(t))}n(De,"ReadableStreamDefaultControllerClose");function Ke(e,t){if(!Je(e))return;const r=e._controlledReadableStream;if(qe(r)&&$t(r)>0)Lr(r,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(u){throw oe(e,u),u}try{Nr(e,t,s)}catch(u){throw oe(e,u),u}}vt(e)}n(Ke,"ReadableStreamDefaultControllerEnqueue");function oe(e,t){const r=e._controlledReadableStream;r._state==="readable"&&(Be(e),ir(e),Zo(r,t))}n(oe,"ReadableStreamDefaultControllerError");function fn(e){const t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}n(fn,"ReadableStreamDefaultControllerGetDesiredSize");function ts(e){return!Vo(e)}n(ts,"ReadableStreamDefaultControllerHasBackpressure");function Je(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}n(Je,"ReadableStreamDefaultControllerCanCloseOrEnqueue");function Qo(e,t,r,s,u,c,d){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Be(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=d,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=u,e._readableStreamController=t;const m=r();g(T(m),()=>(t._started=!0,vt(t),null),R=>(oe(t,R),null))}n(Qo,"SetUpReadableStreamDefaultController");function rs(e,t,r,s){const u=Object.create(ne.prototype);let c,d,m;t.start!==void 0?c=n(()=>t.start(u),"startAlgorithm"):c=n(()=>{},"startAlgorithm"),t.pull!==void 0?d=n(()=>t.pull(u),"pullAlgorithm"):d=n(()=>T(void 0),"pullAlgorithm"),t.cancel!==void 0?m=n(R=>t.cancel(R),"cancelAlgorithm"):m=n(()=>T(void 0),"cancelAlgorithm"),Qo(e,u,c,d,m,r,s)}n(rs,"SetUpReadableStreamDefaultControllerFromUnderlyingSource");function ar(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}n(ar,"defaultControllerBrandCheckException$1");function ns(e,t){return Ie(e._readableStreamController)?is(e):os(e)}n(ns,"ReadableStreamTee");function os(e,t){const r=Qe(e);let s=!1,u=!1,c=!1,d=!1,m,R,y,C,P;const B=A(x=>{P=x});function ae(){return s?(u=!0,T(void 0)):(s=!0,_t(r,{_chunkSteps:n(N=>{ge(()=>{u=!1;const J=N,Ce=N;c||Ke(y._readableStreamController,J),d||Ke(C._readableStreamController,Ce),s=!1,u&&ae()})},"_chunkSteps"),_closeSteps:n(()=>{s=!1,c||De(y._readableStreamController),d||De(C._readableStreamController),(!c||!d)&&P(void 0)},"_closeSteps"),_errorSteps:n(()=>{s=!1},"_errorSteps")}),T(void 0))}n(ae,"pullAlgorithm");function nt(x){if(c=!0,m=x,d){const N=St([m,R]),J=ie(e,N);P(J)}return B}n(nt,"cancel1Algorithm");function Oe(x){if(d=!0,R=x,c){const N=St([m,R]),J=ie(e,N);P(J)}return B}n(Oe,"cancel2Algorithm");function Te(){}return n(Te,"startAlgorithm"),y=Et(Te,ae,nt),C=Et(Te,ae,Oe),I(r._closedPromise,x=>(oe(y._readableStreamController,x),oe(C._readableStreamController,x),(!c||!d)&&P(void 0),null)),[y,C]}n(os,"ReadableStreamDefaultTee");function is(e){let t=Qe(e),r=!1,s=!1,u=!1,c=!1,d=!1,m,R,y,C,P;const B=A(_=>{P=_});function ae(_){I(_._closedPromise,E=>(_!==t||(K(y._readableStreamController,E),K(C._readableStreamController,E),(!c||!d)&&P(void 0)),null))}n(ae,"forwardReaderError");function nt(){je(t)&&(_e(t),t=Qe(e),ae(t)),_t(t,{_chunkSteps:n(E=>{ge(()=>{s=!1,u=!1;const k=E;let Y=E;if(!c&&!d)try{Y=fo(E)}catch(ot){K(y._readableStreamController,ot),K(C._readableStreamController,ot),P(ie(e,ot));return}c||Ht(y._readableStreamController,k),d||Ht(C._readableStreamController,Y),r=!1,s?Te():u&&x()})},"_chunkSteps"),_closeSteps:n(()=>{r=!1,c||wt(y._readableStreamController),d||wt(C._readableStreamController),y._readableStreamController._pendingPullIntos.length>0&&Vt(y._readableStreamController,0),C._readableStreamController._pendingPullIntos.length>0&&Vt(C._readableStreamController,0),(!c||!d)&&P(void 0)},"_closeSteps"),_errorSteps:n(()=>{r=!1},"_errorSteps")})}n(nt,"pullWithDefaultReader");function Oe(_,E){Ee(t)&&(_e(t),t=Co(e),ae(t));const k=E?C:y,Y=E?y:C;Eo(t,_,1,{_chunkSteps:n(it=>{ge(()=>{s=!1,u=!1;const at=E?d:c;if(E?c:d)at||Qt(k._readableStreamController,it);else{let ui;try{ui=fo(it)}catch(kn){K(k._readableStreamController,kn),K(Y._readableStreamController,kn),P(ie(e,kn));return}at||Qt(k._readableStreamController,it),Ht(Y._readableStreamController,ui)}r=!1,s?Te():u&&x()})},"_chunkSteps"),_closeSteps:n(it=>{r=!1;const at=E?d:c,cr=E?c:d;at||wt(k._readableStreamController),cr||wt(Y._readableStreamController),it!==void 0&&(at||Qt(k._readableStreamController,it),!cr&&Y._readableStreamController._pendingPullIntos.length>0&&Vt(Y._readableStreamController,0)),(!at||!cr)&&P(void 0)},"_closeSteps"),_errorSteps:n(()=>{r=!1},"_errorSteps")})}n(Oe,"pullWithBYOBReader");function Te(){if(r)return s=!0,T(void 0);r=!0;const _=Gr(y._readableStreamController);return _===null?nt():Oe(_._view,!1),T(void 0)}n(Te,"pull1Algorithm");function x(){if(r)return u=!0,T(void 0);r=!0;const _=Gr(C._readableStreamController);return _===null?nt():Oe(_._view,!0),T(void 0)}n(x,"pull2Algorithm");function N(_){if(c=!0,m=_,d){const E=St([m,R]),k=ie(e,E);P(k)}return B}n(N,"cancel1Algorithm");function J(_){if(d=!0,R=_,c){const E=St([m,R]),k=ie(e,E);P(k)}return B}n(J,"cancel2Algorithm");function Ce(){}return n(Ce,"startAlgorithm"),y=Go(Ce,Te,N),C=Go(Ce,x,J),ae(t),[y,C]}n(is,"ReadableByteStreamTee");function as(e){return l(e)&&typeof e.getReader<"u"}n(as,"isReadableStreamLike");function ss(e){return as(e)?us(e.getReader()):ls(e)}n(ss,"ReadableStreamFrom");function ls(e){let t;const r=uo(e,"async"),s=f;function u(){let d;try{d=Xi(r)}catch(R){return b(R)}const m=T(d);return F(m,R=>{if(!l(R))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(ea(R))De(t._readableStreamController);else{const C=ta(R);Ke(t._readableStreamController,C)}})}n(u,"pullAlgorithm");function c(d){const m=r.iterator;let R;try{R=Ut(m,"return")}catch(P){return b(P)}if(R===void 0)return T(void 0);let y;try{y=z(R,m,[d])}catch(P){return b(P)}const C=T(y);return F(C,P=>{if(!l(P))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")})}return n(c,"cancelAlgorithm"),t=Et(s,u,c,0),t}n(ls,"ReadableStreamFromIterable");function us(e){let t;const r=f;function s(){let c;try{c=e.read()}catch(d){return b(d)}return F(c,d=>{if(!l(d))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(d.done)De(t._readableStreamController);else{const m=d.value;Ke(t._readableStreamController,m)}})}n(s,"pullAlgorithm");function u(c){try{return T(e.cancel(c))}catch(d){return b(d)}}return n(u,"cancelAlgorithm"),t=Et(r,s,u,0),t}n(us,"ReadableStreamFromDefaultReader");function fs(e,t){ue(e,t);const r=e,s=r?.autoAllocateChunkSize,u=r?.cancel,c=r?.pull,d=r?.start,m=r?.type;return{autoAllocateChunkSize:s===void 0?void 0:Fr(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:u===void 0?void 0:cs(u,r,`${t} has member 'cancel' that`),pull:c===void 0?void 0:ds(c,r,`${t} has member 'pull' that`),start:d===void 0?void 0:hs(d,r,`${t} has member 'start' that`),type:m===void 0?void 0:ps(m,`${t} has member 'type' that`)}}n(fs,"convertUnderlyingDefaultOrByteSource");function cs(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(cs,"convertUnderlyingSourceCancelCallback");function ds(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(ds,"convertUnderlyingSourcePullCallback");function hs(e,t,r){return Z(e,r),s=>z(e,t,[s])}n(hs,"convertUnderlyingSourceStartCallback");function ps(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}n(ps,"convertReadableStreamType");function bs(e,t){return ue(e,t),{preventCancel:!!e?.preventCancel}}n(bs,"convertIteratorOptions");function Yo(e,t){ue(e,t);const r=e?.preventAbort,s=e?.preventCancel,u=e?.preventClose,c=e?.signal;return c!==void 0&&ms(c,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!s,preventClose:!!u,signal:c}}n(Yo,"convertPipeOptions");function ms(e,t){if(!Ca(e))throw new TypeError(`${t} is not an AbortSignal.`)}n(ms,"assertAbortSignal");function ys(e,t){ue(e,t);const r=e?.readable;zr(r,"readable","ReadableWritablePair"),jr(r,`${t} has member 'readable' that`);const s=e?.writable;return zr(s,"writable","ReadableWritablePair"),Bo(s,`${t} has member 'writable' that`),{readable:r,writable:s}}n(ys,"convertReadableWritablePair");const Pn=class Pn{constructor(t={},r={}){t===void 0?t=null:Jn(t,"First parameter");const s=Zt(r,"Second parameter"),u=fs(t,"First parameter");if(cn(this),u.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");const c=Tt(s,0);ca(this,u,c)}else{const c=Gt(s),d=Tt(s,1);rs(this,u,d,c)}}get locked(){if(!We(this))throw Me("locked");return qe(this)}cancel(t=void 0){return We(this)?qe(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):ie(this,t):b(Me("cancel"))}getReader(t=void 0){if(!We(this))throw Me("getReader");return ha(t,"First parameter").mode===void 0?Qe(this):Co(this)}pipeThrough(t,r={}){if(!We(this))throw Me("pipeThrough");Se(t,1,"pipeThrough");const s=ys(t,"First parameter"),u=Yo(r,"Second parameter");if(qe(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Ze(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");const c=Ho(this,s.writable,u.preventClose,u.preventAbort,u.preventCancel,u.signal);return Q(c),s.readable}pipeTo(t,r={}){if(!We(this))return b(Me("pipeTo"));if(t===void 0)return b("Parameter 1 is required in 'pipeTo'.");if(!Ge(t))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=Yo(r,"Second parameter")}catch(u){return b(u)}return qe(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Ze(t)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Ho(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!We(this))throw Me("tee");const t=ns(this);return St(t)}values(t=void 0){if(!We(this))throw Me("values");const r=bs(t,"First parameter");return Ki(this,r.preventCancel)}[Ur](t){return this.values(t)}static from(t){return ss(t)}};n(Pn,"ReadableStream");let L=Pn;Object.defineProperties(L,{from:{enumerable:!0}}),Object.defineProperties(L.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),h(L.from,"from"),h(L.prototype.cancel,"cancel"),h(L.prototype.getReader,"getReader"),h(L.prototype.pipeThrough,"pipeThrough"),h(L.prototype.pipeTo,"pipeTo"),h(L.prototype.tee,"tee"),h(L.prototype.values,"values"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(L.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(L.prototype,Ur,{value:L.prototype.values,writable:!0,configurable:!0});function Et(e,t,r,s=1,u=()=>1){const c=Object.create(L.prototype);cn(c);const d=Object.create(ne.prototype);return Qo(c,d,e,t,r,s,u),c}n(Et,"CreateReadableStream");function Go(e,t,r){const s=Object.create(L.prototype);cn(s);const u=Object.create(te.prototype);return To(s,u,e,t,r,0,void 0),s}n(Go,"CreateReadableByteStream");function cn(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}n(cn,"InitializeReadableStream");function We(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof L}n(We,"IsReadableStream");function qe(e){return e._reader!==void 0}n(qe,"IsReadableStreamLocked");function ie(e,t){if(e._disturbed=!0,e._state==="closed")return T(void 0);if(e._state==="errored")return b(e._storedError);At(e);const r=e._reader;if(r!==void 0&&je(r)){const u=r._readIntoRequests;r._readIntoRequests=new D,u.forEach(c=>{c._closeSteps(void 0)})}const s=e._readableStreamController[Ar](t);return F(s,f)}n(ie,"ReadableStreamCancel");function At(e){e._state="closed";const t=e._reader;if(t!==void 0&&(Zn(t),Ee(t))){const r=t._readRequests;t._readRequests=new D,r.forEach(s=>{s._closeSteps()})}}n(At,"ReadableStreamClose");function Zo(e,t){e._state="errored",e._storedError=t;const r=e._reader;r!==void 0&&(Or(r,t),Ee(r)?ro(r,t):Ao(r,t))}n(Zo,"ReadableStreamError");function Me(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}n(Me,"streamBrandCheckException$1");function Ko(e,t){ue(e,t);const r=e?.highWaterMark;return zr(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Ir(r)}}n(Ko,"convertQueuingStrategyInit");const Jo=n(e=>e.byteLength,"byteLengthSizeFunction");h(Jo,"size");const vn=class vn{constructor(t){Se(t,1,"ByteLengthQueuingStrategy"),t=Ko(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!ei(this))throw Xo("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!ei(this))throw Xo("size");return Jo}};n(vn,"ByteLengthQueuingStrategy");let Xe=vn;Object.defineProperties(Xe.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(Xe.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Xo(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}n(Xo,"byteLengthBrandCheckException");function ei(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof Xe}n(ei,"IsByteLengthQueuingStrategy");const ti=n(()=>1,"countSizeFunction");h(ti,"size");const En=class En{constructor(t){Se(t,1,"CountQueuingStrategy"),t=Ko(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!ni(this))throw ri("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!ni(this))throw ri("size");return ti}};n(En,"CountQueuingStrategy");let et=En;Object.defineProperties(et.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(et.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function ri(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}n(ri,"countBrandCheckException");function ni(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof et}n(ni,"IsCountQueuingStrategy");function gs(e,t){ue(e,t);const r=e?.cancel,s=e?.flush,u=e?.readableType,c=e?.start,d=e?.transform,m=e?.writableType;return{cancel:r===void 0?void 0:Rs(r,e,`${t} has member 'cancel' that`),flush:s===void 0?void 0:_s(s,e,`${t} has member 'flush' that`),readableType:u,start:c===void 0?void 0:Ss(c,e,`${t} has member 'start' that`),transform:d===void 0?void 0:ws(d,e,`${t} has member 'transform' that`),writableType:m}}n(gs,"convertTransformer");function _s(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(_s,"convertTransformerFlushCallback");function Ss(e,t,r){return Z(e,r),s=>z(e,t,[s])}n(Ss,"convertTransformerStartCallback");function ws(e,t,r){return Z(e,r),(s,u)=>j(e,t,[s,u])}n(ws,"convertTransformerTransformCallback");function Rs(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(Rs,"convertTransformerCancelCallback");const An=class An{constructor(t={},r={},s={}){t===void 0&&(t=null);const u=Zt(r,"Second parameter"),c=Zt(s,"Third parameter"),d=gs(t,"First parameter");if(d.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(d.writableType!==void 0)throw new RangeError("Invalid writableType specified");const m=Tt(c,0),R=Gt(c),y=Tt(u,1),C=Gt(u);let P;const B=A(ae=>{P=ae});Ts(this,B,y,C,m,R),Ps(this,d),d.start!==void 0?P(d.start(this._transformStreamController)):P(void 0)}get readable(){if(!oi(this))throw li("readable");return this._readable}get writable(){if(!oi(this))throw li("writable");return this._writable}};n(An,"TransformStream");let tt=An;Object.defineProperties(tt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(tt.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});function Ts(e,t,r,s,u,c){function d(){return t}n(d,"startAlgorithm");function m(B){return As(e,B)}n(m,"writeAlgorithm");function R(B){return Bs(e,B)}n(R,"abortAlgorithm");function y(){return ks(e)}n(y,"closeAlgorithm"),e._writable=Ea(d,m,y,R,r,s);function C(){return Ws(e)}n(C,"pullAlgorithm");function P(B){return qs(e,B)}n(P,"cancelAlgorithm"),e._readable=Et(d,C,P,u,c),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,sr(e,!0),e._transformStreamController=void 0}n(Ts,"InitializeTransformStream");function oi(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof tt}n(oi,"IsTransformStream");function ii(e,t){oe(e._readable._readableStreamController,t),dn(e,t)}n(ii,"TransformStreamError");function dn(e,t){ur(e._transformStreamController),Ct(e._writable._writableStreamController,t),hn(e)}n(dn,"TransformStreamErrorWritableAndUnblockWrite");function hn(e){e._backpressure&&sr(e,!1)}n(hn,"TransformStreamUnblockWrite");function sr(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=A(r=>{e._backpressureChangePromise_resolve=r}),e._backpressure=t}n(sr,"TransformStreamSetBackpressure");const Bn=class Bn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!lr(this))throw fr("desiredSize");const t=this._controlledTransformStream._readable._readableStreamController;return fn(t)}enqueue(t=void 0){if(!lr(this))throw fr("enqueue");ai(this,t)}error(t=void 0){if(!lr(this))throw fr("error");vs(this,t)}terminate(){if(!lr(this))throw fr("terminate");Es(this)}};n(Bn,"TransformStreamDefaultController");let pe=Bn;Object.defineProperties(pe.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),h(pe.prototype.enqueue,"enqueue"),h(pe.prototype.error,"error"),h(pe.prototype.terminate,"terminate"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(pe.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function lr(e){return!l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof pe}n(lr,"IsTransformStreamDefaultController");function Cs(e,t,r,s,u){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=s,t._cancelAlgorithm=u,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}n(Cs,"SetUpTransformStreamDefaultController");function Ps(e,t){const r=Object.create(pe.prototype);let s,u,c;t.transform!==void 0?s=n(d=>t.transform(d,r),"transformAlgorithm"):s=n(d=>{try{return ai(r,d),T(void 0)}catch(m){return b(m)}},"transformAlgorithm"),t.flush!==void 0?u=n(()=>t.flush(r),"flushAlgorithm"):u=n(()=>T(void 0),"flushAlgorithm"),t.cancel!==void 0?c=n(d=>t.cancel(d),"cancelAlgorithm"):c=n(()=>T(void 0),"cancelAlgorithm"),Cs(e,r,s,u,c)}n(Ps,"SetUpTransformStreamDefaultControllerFromTransformer");function ur(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}n(ur,"TransformStreamDefaultControllerClearAlgorithms");function ai(e,t){const r=e._controlledTransformStream,s=r._readable._readableStreamController;if(!Je(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{Ke(s,t)}catch(c){throw dn(r,c),r._readable._storedError}ts(s)!==r._backpressure&&sr(r,!0)}n(ai,"TransformStreamDefaultControllerEnqueue");function vs(e,t){ii(e._controlledTransformStream,t)}n(vs,"TransformStreamDefaultControllerError");function si(e,t){const r=e._transformAlgorithm(t);return F(r,void 0,s=>{throw ii(e._controlledTransformStream,s),s})}n(si,"TransformStreamDefaultControllerPerformTransform");function Es(e){const t=e._controlledTransformStream,r=t._readable._readableStreamController;De(r);const s=new TypeError("TransformStream terminated");dn(t,s)}n(Es,"TransformStreamDefaultControllerTerminate");function As(e,t){const r=e._transformStreamController;if(e._backpressure){const s=e._backpressureChangePromise;return F(s,()=>{const u=e._writable;if(u._state==="erroring")throw u._storedError;return si(r,t)})}return si(r,t)}n(As,"TransformStreamDefaultSinkWriteAlgorithm");function Bs(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._readable;r._finishPromise=A((c,d)=>{r._finishPromise_resolve=c,r._finishPromise_reject=d});const u=r._cancelAlgorithm(t);return ur(r),g(u,()=>(s._state==="errored"?rt(r,s._storedError):(oe(s._readableStreamController,t),pn(r)),null),c=>(oe(s._readableStreamController,c),rt(r,c),null)),r._finishPromise}n(Bs,"TransformStreamDefaultSinkAbortAlgorithm");function ks(e){const t=e._transformStreamController;if(t._finishPromise!==void 0)return t._finishPromise;const r=e._readable;t._finishPromise=A((u,c)=>{t._finishPromise_resolve=u,t._finishPromise_reject=c});const s=t._flushAlgorithm();return ur(t),g(s,()=>(r._state==="errored"?rt(t,r._storedError):(De(r._readableStreamController),pn(t)),null),u=>(oe(r._readableStreamController,u),rt(t,u),null)),t._finishPromise}n(ks,"TransformStreamDefaultSinkCloseAlgorithm");function Ws(e){return sr(e,!1),e._backpressureChangePromise}n(Ws,"TransformStreamDefaultSourcePullAlgorithm");function qs(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._writable;r._finishPromise=A((c,d)=>{r._finishPromise_resolve=c,r._finishPromise_reject=d});const u=r._cancelAlgorithm(t);return ur(r),g(u,()=>(s._state==="errored"?rt(r,s._storedError):(Ct(s._writableStreamController,t),hn(e),pn(r)),null),c=>(Ct(s._writableStreamController,c),hn(e),rt(r,c),null)),r._finishPromise}n(qs,"TransformStreamDefaultSourceCancelAlgorithm");function fr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}n(fr,"defaultControllerBrandCheckException");function pn(e){e._finishPromise_resolve!==void 0&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(pn,"defaultControllerFinishPromiseResolve");function rt(e,t){e._finishPromise_reject!==void 0&&(Q(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(rt,"defaultControllerFinishPromiseReject");function li(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}n(li,"streamBrandCheckException"),a.ByteLengthQueuingStrategy=Xe,a.CountQueuingStrategy=et,a.ReadableByteStreamController=te,a.ReadableStream=L,a.ReadableStreamBYOBReader=ce,a.ReadableStreamBYOBRequest=Re,a.ReadableStreamDefaultController=ne,a.ReadableStreamDefaultReader=fe,a.TransformStream=tt,a.TransformStreamDefaultController=pe,a.WritableStream=de,a.WritableStreamDefaultController=ke,a.WritableStreamDefaultWriter=re})}(kt,kt.exports)),kt.exports}n(Ns,"requirePonyfill_es2018");var mi;function Hs(){if(mi)return pi;mi=1;const i=65536;if(!globalThis.ReadableStream)try{const o=require("node:process"),{emitWarning:a}=o;try{o.emitWarning=()=>{},Object.assign(globalThis,require("node:stream/web")),o.emitWarning=a}catch(f){throw o.emitWarning=a,f}}catch{Object.assign(globalThis,Ns())}try{const{Blob:o}=require("buffer");o&&!o.prototype.stream&&(o.prototype.stream=n(function(f){let l=0;const p=this;return new ReadableStream({type:"bytes",async pull(h){const v=await p.slice(l,Math.min(p.size,l+i)).arrayBuffer();l+=v.byteLength,h.enqueue(new Uint8Array(v)),l===p.size&&h.close()}})},"name"))}catch{}return pi}n(Hs,"requireStreams"),Hs();/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */const yi=65536;async function*Wn(i,o=!0){for(const a of i)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let f=a.byteOffset;const l=a.byteOffset+a.byteLength;for(;f!==l;){const p=Math.min(l-f,yi),h=a.buffer.slice(f,f+p);f+=h.byteLength,yield new Uint8Array(h)}}else yield a;else{let f=0,l=a;for(;f!==l.size;){const h=await l.slice(f,Math.min(l.size,f+yi)).arrayBuffer();f+=h.byteLength,yield new Uint8Array(h)}}}n(Wn,"toIterator");const gi=(ze=class{constructor(o=[],a={}){be(this,ve,[]);be(this,zt,"");be(this,bt,0);be(this,Cr,"transparent");if(typeof o!="object"||o===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof o[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});const f=new TextEncoder;for(const p of o){let h;ArrayBuffer.isView(p)?h=new Uint8Array(p.buffer.slice(p.byteOffset,p.byteOffset+p.byteLength)):p instanceof ArrayBuffer?h=new Uint8Array(p.slice(0)):p instanceof ze?h=p:h=f.encode(`${p}`),X(this,bt,O(this,bt)+(ArrayBuffer.isView(h)?h.byteLength:h.size)),O(this,ve).push(h)}X(this,Cr,`${a.endings===void 0?"transparent":a.endings}`);const l=a.type===void 0?"":String(a.type);X(this,zt,/^[\x20-\x7E]*$/.test(l)?l:"")}get size(){return O(this,bt)}get type(){return O(this,zt)}async text(){const o=new TextDecoder;let a="";for await(const f of Wn(O(this,ve),!1))a+=o.decode(f,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){const o=new Uint8Array(this.size);let a=0;for await(const f of Wn(O(this,ve),!1))o.set(f,a),a+=f.length;return o.buffer}stream(){const o=Wn(O(this,ve),!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){const f=await o.next();f.done?a.close():a.enqueue(f.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,f=""){const{size:l}=this;let p=o<0?Math.max(l+o,0):Math.min(o,l),h=a<0?Math.max(l+a,0):Math.min(a,l);const S=Math.max(h-p,0),v=O(this,ve),w=[];let A=0;for(const b of v){if(A>=S)break;const q=ArrayBuffer.isView(b)?b.byteLength:b.size;if(p&&q<=p)p-=q,h-=q;else{let g;ArrayBuffer.isView(b)?(g=b.subarray(p,Math.min(q,h)),A+=g.byteLength):(g=b.slice(p,Math.min(q,h)),A+=g.size),h-=q,w.push(g),p=0}}const T=new ze([],{type:String(f).toLowerCase()});return X(T,bt,S),X(T,ve,w),T}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](o){return o&&typeof o=="object"&&typeof o.constructor=="function"&&(typeof o.stream=="function"||typeof o.arrayBuffer=="function")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}},ve=new WeakMap,zt=new WeakMap,bt=new WeakMap,Cr=new WeakMap,n(ze,"Blob"),ze);Object.defineProperties(gi.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});const ut=gi,Vs=(mt=class extends ut{constructor(a,f,l={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(a,l);be(this,It,0);be(this,Ft,"");l===null&&(l={});const p=l.lastModified===void 0?Date.now():Number(l.lastModified);Number.isNaN(p)||X(this,It,p),X(this,Ft,String(f))}get name(){return O(this,Ft)}get lastModified(){return O(this,It)}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](a){return!!a&&a instanceof ut&&/^(File)$/.test(a[Symbol.toStringTag])}},It=new WeakMap,Ft=new WeakMap,n(mt,"File"),mt),qn=Vs;/*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var{toStringTag:Wt,iterator:Qs,hasInstance:Ys}=Symbol,_i=Math.random,Gs="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),Si=n((i,o,a)=>(i+="",/^(Blob|File)$/.test(o&&o[Wt])?[(a=a!==void 0?a+"":o[Wt]=="File"?o.name:"blob",i),o.name!==a||o[Wt]=="blob"?new qn([o],a,o):o]:[i,o+""]),"f"),On=n((i,o)=>(o?i:i.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),"e$1"),Ue=n((i,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${i}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},"x");const br=(yt=class{constructor(...o){be(this,ee,[]);if(o.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Wt](){return"FormData"}[Qs](){return this.entries()}static[Ys](o){return o&&typeof o=="object"&&o[Wt]==="FormData"&&!Gs.some(a=>typeof o[a]!="function")}append(...o){Ue("append",arguments,2),O(this,ee).push(Si(...o))}delete(o){Ue("delete",arguments,1),o+="",X(this,ee,O(this,ee).filter(([a])=>a!==o))}get(o){Ue("get",arguments,1),o+="";for(var a=O(this,ee),f=a.length,l=0;l<f;l++)if(a[l][0]===o)return a[l][1];return null}getAll(o,a){return Ue("getAll",arguments,1),a=[],o+="",O(this,ee).forEach(f=>f[0]===o&&a.push(f[1])),a}has(o){return Ue("has",arguments,1),o+="",O(this,ee).some(a=>a[0]===o)}forEach(o,a){Ue("forEach",arguments,1);for(var[f,l]of this)o.call(a,l,f,this)}set(...o){Ue("set",arguments,2);var a=[],f=!0;o=Si(...o),O(this,ee).forEach(l=>{l[0]===o[0]?f&&(f=!a.push(o)):a.push(l)}),f&&a.push(o),X(this,ee,a)}*entries(){yield*O(this,ee)}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}},ee=new WeakMap,n(yt,"FormData"),yt);function Zs(i,o=ut){var a=`${_i()}${_i()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),f=[],l=`--${a}\r
Content-Disposition: form-data; name="`;return i.forEach((p,h)=>typeof p=="string"?f.push(l+On(h)+`"\r
\r
${p.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):f.push(l+On(h)+`"; filename="${On(p.name,1)}"\r
Content-Type: ${p.type||"application/octet-stream"}\r
\r
`,p,`\r
`)),f.push(`--${a}--`),new o(f,{type:"multipart/form-data; boundary="+a})}n(Zs,"formDataToBlob");const Un=class Un extends Error{constructor(o,a){super(o),Error.captureStackTrace(this,this.constructor),this.type=a}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}};n(Un,"FetchBaseError");let ft=Un;const xn=class xn extends ft{constructor(o,a,f){super(o,a),f&&(this.code=this.errno=f.code,this.erroredSysCall=f.syscall)}};n(xn,"FetchError");let G=xn;const mr=Symbol.toStringTag,wi=n(i=>typeof i=="object"&&typeof i.append=="function"&&typeof i.delete=="function"&&typeof i.get=="function"&&typeof i.getAll=="function"&&typeof i.has=="function"&&typeof i.set=="function"&&typeof i.sort=="function"&&i[mr]==="URLSearchParams","isURLSearchParameters"),yr=n(i=>i&&typeof i=="object"&&typeof i.arrayBuffer=="function"&&typeof i.type=="string"&&typeof i.stream=="function"&&typeof i.constructor=="function"&&/^(Blob|File)$/.test(i[mr]),"isBlob"),Ks=n(i=>typeof i=="object"&&(i[mr]==="AbortSignal"||i[mr]==="EventTarget"),"isAbortSignal"),Js=n((i,o)=>{const a=new URL(o).hostname,f=new URL(i).hostname;return a===f||a.endsWith(`.${f}`)},"isDomainOrSubdomain"),Xs=n((i,o)=>{const a=new URL(o).protocol,f=new URL(i).protocol;return a===f},"isSameProtocol"),el=Is(me.pipeline),H=Symbol("Body internals"),Nn=class Nn{constructor(o,{size:a=0}={}){let f=null;o===null?o=null:wi(o)?o=M.from(o.toString()):yr(o)||M.isBuffer(o)||(hr.isAnyArrayBuffer(o)?o=M.from(o):ArrayBuffer.isView(o)?o=M.from(o.buffer,o.byteOffset,o.byteLength):o instanceof me||(o instanceof br?(o=Zs(o),f=o.type.split("=")[1]):o=M.from(String(o))));let l=o;M.isBuffer(o)?l=me.Readable.from(o):yr(o)&&(l=me.Readable.from(o.stream())),this[H]={body:o,stream:l,boundary:f,disturbed:!1,error:null},this.size=a,o instanceof me&&o.on("error",p=>{const h=p instanceof ft?p:new G(`Invalid response body while trying to fetch ${this.url}: ${p.message}`,"system",p);this[H].error=h})}get body(){return this[H].stream}get bodyUsed(){return this[H].disturbed}async arrayBuffer(){const{buffer:o,byteOffset:a,byteLength:f}=await zn(this);return o.slice(a,a+f)}async formData(){const o=this.headers.get("content-type");if(o.startsWith("application/x-www-form-urlencoded")){const f=new br,l=new URLSearchParams(await this.text());for(const[p,h]of l)f.append(p,h);return f}const{toFormData:a}=await import("./chunks/multipart-parser.mjs");return a(this.body,o)}async blob(){const o=this.headers&&this.headers.get("content-type")||this[H].body&&this[H].body.type||"",a=await this.arrayBuffer();return new ut([a],{type:o})}async json(){const o=await this.text();return JSON.parse(o)}async text(){const o=await zn(this);return new TextDecoder().decode(o)}buffer(){return zn(this)}};n(Nn,"Body");let xe=Nn;xe.prototype.buffer=pr(xe.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer"),Object.defineProperties(xe.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:pr(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function zn(i){if(i[H].disturbed)throw new TypeError(`body used already for: ${i.url}`);if(i[H].disturbed=!0,i[H].error)throw i[H].error;const{body:o}=i;if(o===null)return M.alloc(0);if(!(o instanceof me))return M.alloc(0);const a=[];let f=0;try{for await(const l of o){if(i.size>0&&f+l.length>i.size){const p=new G(`content size at ${i.url} over limit: ${i.size}`,"max-size");throw o.destroy(p),p}f+=l.length,a.push(l)}}catch(l){throw l instanceof ft?l:new G(`Invalid response body while trying to fetch ${i.url}: ${l.message}`,"system",l)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(l=>typeof l=="string")?M.from(a.join("")):M.concat(a,f)}catch(l){throw new G(`Could not create Buffer from response body for ${i.url}: ${l.message}`,"system",l)}else throw new G(`Premature close of server response while trying to fetch ${i.url}`)}n(zn,"consumeBody");const In=n((i,o)=>{let a,f,{body:l}=i[H];if(i.bodyUsed)throw new Error("cannot clone body after it is used");return l instanceof me&&typeof l.getBoundary!="function"&&(a=new dr({highWaterMark:o}),f=new dr({highWaterMark:o}),l.pipe(a),l.pipe(f),i[H].stream=a,l=f),l},"clone"),tl=pr(i=>i.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),Ri=n((i,o)=>i===null?null:typeof i=="string"?"text/plain;charset=UTF-8":wi(i)?"application/x-www-form-urlencoded;charset=UTF-8":yr(i)?i.type||null:M.isBuffer(i)||hr.isAnyArrayBuffer(i)||ArrayBuffer.isView(i)?null:i instanceof br?`multipart/form-data; boundary=${o[H].boundary}`:i&&typeof i.getBoundary=="function"?`multipart/form-data;boundary=${tl(i)}`:i instanceof me?null:"text/plain;charset=UTF-8","extractContentType"),rl=n(i=>{const{body:o}=i[H];return o===null?0:yr(o)?o.size:M.isBuffer(o)?o.length:o&&typeof o.getLengthSync=="function"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},"getTotalBytes"),nl=n(async(i,{body:o})=>{o===null?i.end():await el(o,i)},"writeToStream"),gr=typeof Bt.validateHeaderName=="function"?Bt.validateHeaderName:i=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(i)){const o=new TypeError(`Header name must be a valid HTTP token [${i}]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),o}},Fn=typeof Bt.validateHeaderValue=="function"?Bt.validateHeaderValue:(i,o)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(o)){const a=new TypeError(`Invalid character in header content ["${i}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},Pr=class Pr extends URLSearchParams{constructor(o){let a=[];if(o instanceof Pr){const f=o.raw();for(const[l,p]of Object.entries(f))a.push(...p.map(h=>[l,h]))}else if(o!=null)if(typeof o=="object"&&!hr.isBoxedPrimitive(o)){const f=o[Symbol.iterator];if(f==null)a.push(...Object.entries(o));else{if(typeof f!="function")throw new TypeError("Header pairs must be iterable");a=[...o].map(l=>{if(typeof l!="object"||hr.isBoxedPrimitive(l))throw new TypeError("Each header pair must be an iterable object");return[...l]}).map(l=>{if(l.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...l]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return a=a.length>0?a.map(([f,l])=>(gr(f),Fn(f,String(l)),[String(f).toLowerCase(),String(l)])):void 0,super(a),new Proxy(this,{get(f,l,p){switch(l){case"append":case"set":return(h,S)=>(gr(h),Fn(h,String(S)),URLSearchParams.prototype[l].call(f,String(h).toLowerCase(),String(S)));case"delete":case"has":case"getAll":return h=>(gr(h),URLSearchParams.prototype[l].call(f,String(h).toLowerCase()));case"keys":return()=>(f.sort(),new Set(URLSearchParams.prototype.keys.call(f)).keys());default:return Reflect.get(f,l,p)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(o){const a=this.getAll(o);if(a.length===0)return null;let f=a.join(", ");return/^content-encoding$/i.test(o)&&(f=f.toLowerCase()),f}forEach(o,a=void 0){for(const f of this.keys())Reflect.apply(o,a,[this.get(f),f,this])}*values(){for(const o of this.keys())yield this.get(o)}*entries(){for(const o of this.keys())yield[o,this.get(o)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((o,a)=>(o[a]=this.getAll(a),o),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((o,a)=>{const f=this.getAll(a);return a==="host"?o[a]=f[0]:o[a]=f.length>1?f:f[0],o},{})}};n(Pr,"Headers");let ye=Pr;Object.defineProperties(ye.prototype,["get","entries","forEach","values"].reduce((i,o)=>(i[o]={enumerable:!0},i),{}));function ol(i=[]){return new ye(i.reduce((o,a,f,l)=>(f%2===0&&o.push(l.slice(f,f+2)),o),[]).filter(([o,a])=>{try{return gr(o),Fn(o,String(a)),!0}catch{return!1}}))}n(ol,"fromRawHeaders");const il=new Set([301,302,303,307,308]),jn=n(i=>il.has(i),"isRedirect"),se=Symbol("Response internals"),Ne=class Ne extends xe{constructor(o=null,a={}){super(o,a);const f=a.status!=null?a.status:200,l=new ye(a.headers);if(o!==null&&!l.has("Content-Type")){const p=Ri(o,this);p&&l.append("Content-Type",p)}this[se]={type:"default",url:a.url,status:f,statusText:a.statusText||"",headers:l,counter:a.counter,highWaterMark:a.highWaterMark}}get type(){return this[se].type}get url(){return this[se].url||""}get status(){return this[se].status}get ok(){return this[se].status>=200&&this[se].status<300}get redirected(){return this[se].counter>0}get statusText(){return this[se].statusText}get headers(){return this[se].headers}get highWaterMark(){return this[se].highWaterMark}clone(){return new Ne(In(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(o,a=302){if(!jn(a))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new Ne(null,{headers:{location:new URL(o).toString()},status:a})}static error(){const o=new Ne(null,{status:0,statusText:""});return o[se].type="error",o}static json(o=void 0,a={}){const f=JSON.stringify(o);if(f===void 0)throw new TypeError("data is not JSON serializable");const l=new ye(a&&a.headers);return l.has("content-type")||l.set("content-type","application/json"),new Ne(f,{...a,headers:l})}get[Symbol.toStringTag](){return"Response"}};n(Ne,"Response");let le=Ne;Object.defineProperties(le.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});const al=n(i=>{if(i.search)return i.search;const o=i.href.length-1,a=i.hash||(i.href[o]==="#"?"#":"");return i.href[o-a.length]==="?"?"?":""},"getSearch");function Ti(i,o=!1){return i==null||(i=new URL(i),/^(about|blob|data):$/.test(i.protocol))?"no-referrer":(i.username="",i.password="",i.hash="",o&&(i.pathname="",i.search=""),i)}n(Ti,"stripURLForUseAsAReferrer");const Ci=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),sl="strict-origin-when-cross-origin";function ll(i){if(!Ci.has(i))throw new TypeError(`Invalid referrerPolicy: ${i}`);return i}n(ll,"validateReferrerPolicy");function ul(i){if(/^(http|ws)s:$/.test(i.protocol))return!0;const o=i.host.replace(/(^\[)|(]$)/g,""),a=Ls(o);return a===4&&/^127\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:i.host==="localhost"||i.host.endsWith(".localhost")?!1:i.protocol==="file:"}n(ul,"isOriginPotentiallyTrustworthy");function ct(i){return/^about:(blank|srcdoc)$/.test(i)||i.protocol==="data:"||/^(blob|filesystem):$/.test(i.protocol)?!0:ul(i)}n(ct,"isUrlPotentiallyTrustworthy");function fl(i,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(i.referrer==="no-referrer"||i.referrerPolicy==="")return null;const f=i.referrerPolicy;if(i.referrer==="about:client")return"no-referrer";const l=i.referrer;let p=Ti(l),h=Ti(l,!0);p.toString().length>4096&&(p=h),o&&(p=o(p)),a&&(h=a(h));const S=new URL(i.url);switch(f){case"no-referrer":return"no-referrer";case"origin":return h;case"unsafe-url":return p;case"strict-origin":return ct(p)&&!ct(S)?"no-referrer":h.toString();case"strict-origin-when-cross-origin":return p.origin===S.origin?p:ct(p)&&!ct(S)?"no-referrer":h;case"same-origin":return p.origin===S.origin?p:"no-referrer";case"origin-when-cross-origin":return p.origin===S.origin?p:h;case"no-referrer-when-downgrade":return ct(p)&&!ct(S)?"no-referrer":p;default:throw new TypeError(`Invalid referrerPolicy: ${f}`)}}n(fl,"determineRequestsReferrer");function cl(i){const o=(i.get("referrer-policy")||"").split(/[,\s]+/);let a="";for(const f of o)f&&Ci.has(f)&&(a=f);return a}n(cl,"parseReferrerPolicyFromHeader");const $=Symbol("Request internals"),qt=n(i=>typeof i=="object"&&typeof i[$]=="object","isRequest"),dl=pr(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),vr=class vr extends xe{constructor(o,a={}){let f;if(qt(o)?f=new URL(o.url):(f=new URL(o),o={}),f.username!==""||f.password!=="")throw new TypeError(`${f} is an url with embedded credentials.`);let l=a.method||o.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(l)&&(l=l.toUpperCase()),!qt(a)&&"data"in a&&dl(),(a.body!=null||qt(o)&&o.body!==null)&&(l==="GET"||l==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");const p=a.body?a.body:qt(o)&&o.body!==null?In(o):null;super(p,{size:a.size||o.size||0});const h=new ye(a.headers||o.headers||{});if(p!==null&&!h.has("Content-Type")){const w=Ri(p,this);w&&h.set("Content-Type",w)}let S=qt(o)?o.signal:null;if("signal"in a&&(S=a.signal),S!=null&&!Ks(S))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let v=a.referrer==null?o.referrer:a.referrer;if(v==="")v="no-referrer";else if(v){const w=new URL(v);v=/^about:(\/\/)?client$/.test(w)?"client":w}else v=void 0;this[$]={method:l,redirect:a.redirect||o.redirect||"follow",headers:h,parsedURL:f,signal:S,referrer:v},this.follow=a.follow===void 0?o.follow===void 0?20:o.follow:a.follow,this.compress=a.compress===void 0?o.compress===void 0?!0:o.compress:a.compress,this.counter=a.counter||o.counter||0,this.agent=a.agent||o.agent,this.highWaterMark=a.highWaterMark||o.highWaterMark||16384,this.insecureHTTPParser=a.insecureHTTPParser||o.insecureHTTPParser||!1,this.referrerPolicy=a.referrerPolicy||o.referrerPolicy||""}get method(){return this[$].method}get url(){return js(this[$].parsedURL)}get headers(){return this[$].headers}get redirect(){return this[$].redirect}get signal(){return this[$].signal}get referrer(){if(this[$].referrer==="no-referrer")return"";if(this[$].referrer==="client")return"about:client";if(this[$].referrer)return this[$].referrer.toString()}get referrerPolicy(){return this[$].referrerPolicy}set referrerPolicy(o){this[$].referrerPolicy=ll(o)}clone(){return new vr(this)}get[Symbol.toStringTag](){return"Request"}};n(vr,"Request");let dt=vr;Object.defineProperties(dt.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});const hl=n(i=>{const{parsedURL:o}=i[$],a=new ye(i[$].headers);a.has("Accept")||a.set("Accept","*/*");let f=null;if(i.body===null&&/^(post|put)$/i.test(i.method)&&(f="0"),i.body!==null){const S=rl(i);typeof S=="number"&&!Number.isNaN(S)&&(f=String(S))}f&&a.set("Content-Length",f),i.referrerPolicy===""&&(i.referrerPolicy=sl),i.referrer&&i.referrer!=="no-referrer"?i[$].referrer=fl(i):i[$].referrer="no-referrer",i[$].referrer instanceof URL&&a.set("Referer",i.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),i.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:l}=i;typeof l=="function"&&(l=l(o));const p=al(o),h={path:o.pathname+p,method:i.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:i.insecureHTTPParser,agent:l};return{parsedURL:o,options:h}},"getNodeRequestOptions"),Hn=class Hn extends ft{constructor(o,a="aborted"){super(o,a)}};n(Hn,"AbortError");let _r=Hn;/*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var Ln,Pi;function pl(){if(Pi)return Ln;if(Pi=1,!globalThis.DOMException)try{const{MessageChannel:i}=require("worker_threads"),o=new i().port1,a=new ArrayBuffer;o.postMessage(a,[a,a])}catch(i){i.constructor.name==="DOMException"&&(globalThis.DOMException=i.constructor)}return Ln=globalThis.DOMException,Ln}n(pl,"requireNodeDomexception");var bl=pl();const ml=Fs(bl),{stat:$n}=$s,yl=n((i,o)=>vi(hi(i),i,o),"blobFromSync"),gl=n((i,o)=>$n(i).then(a=>vi(a,i,o)),"blobFrom"),_l=n((i,o)=>$n(i).then(a=>Ei(a,i,o)),"fileFrom"),Sl=n((i,o)=>Ei(hi(i),i,o),"fileFromSync"),vi=n((i,o,a="")=>new ut([new Sr({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],{type:a}),"fromBlob"),Ei=n((i,o,a="")=>new qn([new Sr({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],Ms(o),{type:a,lastModified:i.mtimeMs}),"fromFile"),Er=class Er{constructor(o){be(this,He);be(this,Ve);X(this,He,o.path),X(this,Ve,o.start),this.size=o.size,this.lastModified=o.lastModified}slice(o,a){return new Er({path:O(this,He),lastModified:this.lastModified,size:a-o,start:O(this,Ve)+o})}async*stream(){const{mtimeMs:o}=await $n(O(this,He));if(o>this.lastModified)throw new ml("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*Ds(O(this,He),{start:O(this,Ve),end:O(this,Ve)+this.size-1})}get[Symbol.toStringTag](){return"Blob"}};He=new WeakMap,Ve=new WeakMap,n(Er,"BlobDataItem");let Sr=Er;const wl=new Set(["data:","http:","https:"]);async function Ai(i,o){return new Promise((a,f)=>{const l=new dt(i,o),{parsedURL:p,options:h}=hl(l);if(!wl.has(p.protocol))throw new TypeError(`node-fetch cannot load ${i}. URL scheme "${p.protocol.replace(/:$/,"")}" is not supported.`);if(p.protocol==="data:"){const g=Us(l.url),V=new le(g,{headers:{"Content-Type":g.typeFull}});a(V);return}const S=(p.protocol==="https:"?zs:Bt).request,{signal:v}=l;let w=null;const A=n(()=>{const g=new _r("The operation was aborted.");f(g),l.body&&l.body instanceof me.Readable&&l.body.destroy(g),!(!w||!w.body)&&w.body.emit("error",g)},"abort");if(v&&v.aborted){A();return}const T=n(()=>{A(),q()},"abortAndFinalize"),b=S(p.toString(),h);v&&v.addEventListener("abort",T);const q=n(()=>{b.abort(),v&&v.removeEventListener("abort",T)},"finalize");b.on("error",g=>{f(new G(`request to ${l.url} failed, reason: ${g.message}`,"system",g)),q()}),Rl(b,g=>{w&&w.body&&w.body.destroy(g)}),process.version<"v14"&&b.on("socket",g=>{let V;g.prependListener("end",()=>{V=g._eventsCount}),g.prependListener("close",I=>{if(w&&V<g._eventsCount&&!I){const F=new Error("Premature close");F.code="ERR_STREAM_PREMATURE_CLOSE",w.body.emit("error",F)}})}),b.on("response",g=>{b.setTimeout(0);const V=ol(g.rawHeaders);if(jn(g.statusCode)){const z=V.get("Location");let j=null;try{j=z===null?null:new URL(z,l.url)}catch{if(l.redirect!=="manual"){f(new G(`uri requested responds with an invalid redirect URL: ${z}`,"invalid-redirect")),q();return}}switch(l.redirect){case"error":f(new G(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),q();return;case"manual":break;case"follow":{if(j===null)break;if(l.counter>=l.follow){f(new G(`maximum redirect reached at: ${l.url}`,"max-redirect")),q();return}const U={headers:new ye(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:In(l),signal:l.signal,size:l.size,referrer:l.referrer,referrerPolicy:l.referrerPolicy};if(!Js(l.url,j)||!Xs(l.url,j))for(const jt of["authorization","www-authenticate","cookie","cookie2"])U.headers.delete(jt);if(g.statusCode!==303&&l.body&&o.body instanceof me.Readable){f(new G("Cannot follow redirect with body being a readable stream","unsupported-redirect")),q();return}(g.statusCode===303||(g.statusCode===301||g.statusCode===302)&&l.method==="POST")&&(U.method="GET",U.body=void 0,U.headers.delete("content-length"));const D=cl(V);D&&(U.referrerPolicy=D),a(Ai(new dt(j,U))),q();return}default:return f(new TypeError(`Redirect option '${l.redirect}' is not a valid value of RequestRedirect`))}}v&&g.once("end",()=>{v.removeEventListener("abort",T)});let I=lt(g,new dr,z=>{z&&f(z)});process.version<"v12.10"&&g.on("aborted",T);const F={url:l.url,status:g.statusCode,statusText:g.statusMessage,headers:V,size:l.size,counter:l.counter,highWaterMark:l.highWaterMark},Q=V.get("Content-Encoding");if(!l.compress||l.method==="HEAD"||Q===null||g.statusCode===204||g.statusCode===304){w=new le(I,F),a(w);return}const ge={flush:st.Z_SYNC_FLUSH,finishFlush:st.Z_SYNC_FLUSH};if(Q==="gzip"||Q==="x-gzip"){I=lt(I,st.createGunzip(ge),z=>{z&&f(z)}),w=new le(I,F),a(w);return}if(Q==="deflate"||Q==="x-deflate"){const z=lt(g,new dr,j=>{j&&f(j)});z.once("data",j=>{(j[0]&15)===8?I=lt(I,st.createInflate(),U=>{U&&f(U)}):I=lt(I,st.createInflateRaw(),U=>{U&&f(U)}),w=new le(I,F),a(w)}),z.once("end",()=>{w||(w=new le(I,F),a(w))});return}if(Q==="br"){I=lt(I,st.createBrotliDecompress(),z=>{z&&f(z)}),w=new le(I,F),a(w);return}w=new le(I,F),a(w)}),nl(b,l).catch(f)})}n(Ai,"fetch$1");function Rl(i,o){const a=M.from(`0\r
\r
`);let f=!1,l=!1,p;i.on("response",h=>{const{headers:S}=h;f=S["transfer-encoding"]==="chunked"&&!S["content-length"]}),i.on("socket",h=>{const S=n(()=>{if(f&&!l){const w=new Error("Premature close");w.code="ERR_STREAM_PREMATURE_CLOSE",o(w)}},"onSocketClose"),v=n(w=>{l=M.compare(w.slice(-5),a)===0,!l&&p&&(l=M.compare(p.slice(-3),a.slice(0,3))===0&&M.compare(w.slice(-2),a.slice(3))===0),p=w},"onData");h.prependListener("close",S),h.on("data",v),i.on("close",()=>{h.removeListener("close",S),h.removeListener("data",v)})})}n(Rl,"fixResponseChunkedTransferBadEnding");const Bi=new WeakMap,Dn=new WeakMap;function W(i){const o=Bi.get(i);return console.assert(o!=null,"'this' is expected an Event object, but got",i),o}n(W,"pd");function ki(i){if(i.passiveListener!=null){typeof console<"u"&&typeof console.error=="function"&&console.error("Unable to preventDefault inside passive event listener invocation.",i.passiveListener);return}i.event.cancelable&&(i.canceled=!0,typeof i.event.preventDefault=="function"&&i.event.preventDefault())}n(ki,"setCancelFlag");function ht(i,o){Bi.set(this,{eventTarget:i,event:o,eventPhase:2,currentTarget:i,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:o.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});const a=Object.keys(o);for(let f=0;f<a.length;++f){const l=a[f];l in this||Object.defineProperty(this,l,Wi(l))}}n(ht,"Event"),ht.prototype={get type(){return W(this).event.type},get target(){return W(this).eventTarget},get currentTarget(){return W(this).currentTarget},composedPath(){const i=W(this).currentTarget;return i==null?[]:[i]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return W(this).eventPhase},stopPropagation(){const i=W(this);i.stopped=!0,typeof i.event.stopPropagation=="function"&&i.event.stopPropagation()},stopImmediatePropagation(){const i=W(this);i.stopped=!0,i.immediateStopped=!0,typeof i.event.stopImmediatePropagation=="function"&&i.event.stopImmediatePropagation()},get bubbles(){return!!W(this).event.bubbles},get cancelable(){return!!W(this).event.cancelable},preventDefault(){ki(W(this))},get defaultPrevented(){return W(this).canceled},get composed(){return!!W(this).event.composed},get timeStamp(){return W(this).timeStamp},get srcElement(){return W(this).eventTarget},get cancelBubble(){return W(this).stopped},set cancelBubble(i){if(!i)return;const o=W(this);o.stopped=!0,typeof o.event.cancelBubble=="boolean"&&(o.event.cancelBubble=!0)},get returnValue(){return!W(this).canceled},set returnValue(i){i||ki(W(this))},initEvent(){}},Object.defineProperty(ht.prototype,"constructor",{value:ht,configurable:!0,writable:!0}),typeof window<"u"&&typeof window.Event<"u"&&(Object.setPrototypeOf(ht.prototype,window.Event.prototype),Dn.set(window.Event.prototype,ht));function Wi(i){return{get(){return W(this).event[i]},set(o){W(this).event[i]=o},configurable:!0,enumerable:!0}}n(Wi,"defineRedirectDescriptor");function Tl(i){return{value(){const o=W(this).event;return o[i].apply(o,arguments)},configurable:!0,enumerable:!0}}n(Tl,"defineCallDescriptor");function Cl(i,o){const a=Object.keys(o);if(a.length===0)return i;function f(l,p){i.call(this,l,p)}n(f,"CustomEvent"),f.prototype=Object.create(i.prototype,{constructor:{value:f,configurable:!0,writable:!0}});for(let l=0;l<a.length;++l){const p=a[l];if(!(p in i.prototype)){const S=typeof Object.getOwnPropertyDescriptor(o,p).value=="function";Object.defineProperty(f.prototype,p,S?Tl(p):Wi(p))}}return f}n(Cl,"defineWrapper");function qi(i){if(i==null||i===Object.prototype)return ht;let o=Dn.get(i);return o==null&&(o=Cl(qi(Object.getPrototypeOf(i)),i),Dn.set(i,o)),o}n(qi,"getWrapper");function Pl(i,o){const a=qi(Object.getPrototypeOf(o));return new a(i,o)}n(Pl,"wrapEvent");function vl(i){return W(i).immediateStopped}n(vl,"isStopped");function El(i,o){W(i).eventPhase=o}n(El,"setEventPhase");function Al(i,o){W(i).currentTarget=o}n(Al,"setCurrentTarget");function Oi(i,o){W(i).passiveListener=o}n(Oi,"setPassiveListener");const zi=new WeakMap,Ii=1,Fi=2,wr=3;function Rr(i){return i!==null&&typeof i=="object"}n(Rr,"isObject");function Ot(i){const o=zi.get(i);if(o==null)throw new TypeError("'this' is expected an EventTarget object, but got another value.");return o}n(Ot,"getListeners");function Bl(i){return{get(){let a=Ot(this).get(i);for(;a!=null;){if(a.listenerType===wr)return a.listener;a=a.next}return null},set(o){typeof o!="function"&&!Rr(o)&&(o=null);const a=Ot(this);let f=null,l=a.get(i);for(;l!=null;)l.listenerType===wr?f!==null?f.next=l.next:l.next!==null?a.set(i,l.next):a.delete(i):f=l,l=l.next;if(o!==null){const p={listener:o,listenerType:wr,passive:!1,once:!1,next:null};f===null?a.set(i,p):f.next=p}},configurable:!0,enumerable:!0}}n(Bl,"defineEventAttributeDescriptor");function ji(i,o){Object.defineProperty(i,`on${o}`,Bl(o))}n(ji,"defineEventAttribute");function Li(i){function o(){Pe.call(this)}n(o,"CustomEventTarget"),o.prototype=Object.create(Pe.prototype,{constructor:{value:o,configurable:!0,writable:!0}});for(let a=0;a<i.length;++a)ji(o.prototype,i[a]);return o}n(Li,"defineCustomEventTarget");function Pe(){if(this instanceof Pe){zi.set(this,new Map);return}if(arguments.length===1&&Array.isArray(arguments[0]))return Li(arguments[0]);if(arguments.length>0){const i=new Array(arguments.length);for(let o=0;o<arguments.length;++o)i[o]=arguments[o];return Li(i)}throw new TypeError("Cannot call a class as a function")}n(Pe,"EventTarget"),Pe.prototype={addEventListener(i,o,a){if(o==null)return;if(typeof o!="function"&&!Rr(o))throw new TypeError("'listener' should be a function or an object.");const f=Ot(this),l=Rr(a),h=(l?!!a.capture:!!a)?Ii:Fi,S={listener:o,listenerType:h,passive:l&&!!a.passive,once:l&&!!a.once,next:null};let v=f.get(i);if(v===void 0){f.set(i,S);return}let w=null;for(;v!=null;){if(v.listener===o&&v.listenerType===h)return;w=v,v=v.next}w.next=S},removeEventListener(i,o,a){if(o==null)return;const f=Ot(this),p=(Rr(a)?!!a.capture:!!a)?Ii:Fi;let h=null,S=f.get(i);for(;S!=null;){if(S.listener===o&&S.listenerType===p){h!==null?h.next=S.next:S.next!==null?f.set(i,S.next):f.delete(i);return}h=S,S=S.next}},dispatchEvent(i){if(i==null||typeof i.type!="string")throw new TypeError('"event.type" should be a string.');const o=Ot(this),a=i.type;let f=o.get(a);if(f==null)return!0;const l=Pl(this,i);let p=null;for(;f!=null;){if(f.once?p!==null?p.next=f.next:f.next!==null?o.set(a,f.next):o.delete(a):p=f,Oi(l,f.passive?f.listener:null),typeof f.listener=="function")try{f.listener.call(this,l)}catch(h){typeof console<"u"&&typeof console.error=="function"&&console.error(h)}else f.listenerType!==wr&&typeof f.listener.handleEvent=="function"&&f.listener.handleEvent(l);if(vl(l))break;f=f.next}return Oi(l,null),El(l,0),Al(l,null),!l.defaultPrevented}},Object.defineProperty(Pe.prototype,"constructor",{value:Pe,configurable:!0,writable:!0}),typeof window<"u"&&typeof window.EventTarget<"u"&&Object.setPrototypeOf(Pe.prototype,window.EventTarget.prototype);const Vn=class Vn extends Pe{constructor(){throw super(),new TypeError("AbortSignal cannot be constructed directly")}get aborted(){const o=Tr.get(this);if(typeof o!="boolean")throw new TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?"null":typeof this}`);return o}};n(Vn,"AbortSignal");let pt=Vn;ji(pt.prototype,"abort");function kl(){const i=Object.create(pt.prototype);return Pe.call(i),Tr.set(i,!1),i}n(kl,"createAbortSignal");function Wl(i){Tr.get(i)===!1&&(Tr.set(i,!0),i.dispatchEvent({type:"abort"}))}n(Wl,"abortSignal");const Tr=new WeakMap;Object.defineProperties(pt.prototype,{aborted:{enumerable:!0}}),typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(pt.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortSignal"});let Mn=(gt=class{constructor(){$i.set(this,kl())}get signal(){return Di(this)}abort(){Wl(Di(this))}},n(gt,"AbortController"),gt);const $i=new WeakMap;function Di(i){const o=$i.get(i);if(o==null)throw new TypeError(`Expected 'this' to be an 'AbortController' object, but got ${i===null?"null":typeof i}`);return o}n(Di,"getSignal"),Object.defineProperties(Mn.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(Mn.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortController"});var ql=Object.defineProperty,Ol=n((i,o)=>ql(i,"name",{value:o,configurable:!0}),"e");const Mi=Ai;Ui();function Ui(){!globalThis.process?.versions?.node&&!globalThis.process?.env?.DISABLE_NODE_FETCH_NATIVE_WARN&&console.warn("[node-fetch-native] Node.js compatible build of `node-fetch-native` is being used in a non-Node.js environment. Please make sure you are using proper export conditions or report this issue to https://github.com/unjs/node-fetch-native. You can set `process.env.DISABLE_NODE_FETCH_NATIVE_WARN` to disable this warning.")}n(Ui,"s"),Ol(Ui,"checkNodeEnvironment");export{Mn as AbortController,_r as AbortError,ut as Blob,G as FetchError,qn as File,br as FormData,ye as Headers,dt as Request,le as Response,gl as blobFrom,yl as blobFromSync,Mi as default,Mi as fetch,_l as fileFrom,Sl as fileFromSync,jn as isRedirect};
