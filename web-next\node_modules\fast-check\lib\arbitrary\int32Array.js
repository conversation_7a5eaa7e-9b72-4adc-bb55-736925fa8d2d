"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.int32Array = int32Array;
const globals_1 = require("../utils/globals");
const integer_1 = require("./integer");
const TypedIntArrayArbitraryBuilder_1 = require("./_internals/builders/TypedIntArrayArbitraryBuilder");
function int32Array(constraints = {}) {
    return (0, TypedIntArrayArbitraryBuilder_1.typedIntArrayArbitraryArbitraryBuilder)(constraints, -0x80000000, 0x7fffffff, globals_1.Int32Array, integer_1.integer);
}
