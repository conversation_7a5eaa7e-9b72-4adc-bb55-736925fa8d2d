"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OP_SUSPEND = exports.OP_SUCCEED_NOW = exports.OP_SUCCEED = exports.OP_READ = exports.OP_PROVIDE = exports.OP_PIPE_TO = exports.OP_FROM_EFFECT = exports.OP_FOLD = exports.OP_FAIL = exports.OP_ENSURING = exports.OP_EMIT = exports.OP_CONCAT_ALL = exports.OP_BRIDGE = exports.OP_BRACKET_OUT = void 0;
/** @internal */
const OP_BRACKET_OUT = exports.OP_BRACKET_OUT = "BracketOut";
/** @internal */
const OP_BRIDGE = exports.OP_BRIDGE = "Bridge";
/** @internal */
const OP_CONCAT_ALL = exports.OP_CONCAT_ALL = "ConcatAll";
/** @internal */
const OP_EMIT = exports.OP_EMIT = "Emit";
/** @internal */
const OP_ENSURING = exports.OP_ENSURING = "Ensuring";
/** @internal */
const OP_FAIL = exports.OP_FAIL = "Fail";
/** @internal */
const OP_FOLD = exports.OP_FOLD = "Fold";
/** @internal */
const OP_FROM_EFFECT = exports.OP_FROM_EFFECT = "FromEffect";
/** @internal */
const OP_PIPE_TO = exports.OP_PIPE_TO = "PipeTo";
/** @internal */
const OP_PROVIDE = exports.OP_PROVIDE = "Provide";
/** @internal */
const OP_READ = exports.OP_READ = "Read";
/** @internal */
const OP_SUCCEED = exports.OP_SUCCEED = "Succeed";
/** @internal */
const OP_SUCCEED_NOW = exports.OP_SUCCEED_NOW = "SucceedNow";
/** @internal */
const OP_SUSPEND = exports.OP_SUSPEND = "Suspend";
//# sourceMappingURL=channel.js.map