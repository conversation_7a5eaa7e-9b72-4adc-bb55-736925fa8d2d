"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isYield = exports.isContinue = exports.isClose = exports.isChildExecutorDecision = exports.Yield = exports.Continue = exports.Close = exports.ChildExecutorDecisionTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/channel/childExecutorDecision.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const ChildExecutorDecisionTypeId = exports.ChildExecutorDecisionTypeId = internal.ChildExecutorDecisionTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const Continue = exports.Continue = internal.Continue;
/**
 * @since 2.0.0
 * @category constructors
 */
const Close = exports.Close = internal.Close;
/**
 * @since 2.0.0
 * @category constructors
 */
const Yield = exports.Yield = internal.Yield;
/**
 * Returns `true` if the specified value is a `ChildExecutorDecision`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isChildExecutorDecision = exports.isChildExecutorDecision = internal.isChildExecutorDecision;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Continue`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isContinue = exports.isContinue = internal.isContinue;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Close`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isClose = exports.isClose = internal.isClose;
/**
 * Returns `true` if the specified `ChildExecutorDecision` is a `Yield`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isYield = exports.isYield = internal.isYield;
/**
 * Folds over a `ChildExecutorDecision` to produce a value of type `A`.
 *
 * @since 2.0.0
 * @category folding
 */
const match = exports.match = internal.match;
//# sourceMappingURL=ChildExecutorDecision.js.map