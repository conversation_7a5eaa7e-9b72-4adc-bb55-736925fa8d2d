"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uint32Array = uint32Array;
const globals_1 = require("../utils/globals");
const integer_1 = require("./integer");
const TypedIntArrayArbitraryBuilder_1 = require("./_internals/builders/TypedIntArrayArbitraryBuilder");
function uint32Array(constraints = {}) {
    return (0, TypedIntArrayArbitraryBuilder_1.typedIntArrayArbitraryArbitraryBuilder)(constraints, 0, 0xffffffff, globals_1.Uint32Array, integer_1.integer);
}
